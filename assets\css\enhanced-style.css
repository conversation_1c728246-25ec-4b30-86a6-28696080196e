/**
 * Enhanced CSS Styles
 * نظام إدارة العلاوات والترقية وكتب الشكر
 * Modern UI with beautiful color scheme
 */

/* Import <PERSON>jawal font for better Arabic typography */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* Variables */
:root {
    /* Primary Colors - Beautiful Blue Theme */
    --primary-color: #1a73e8;       /* Google Blue */
    --primary-light: #4285f4;       /* Lighter blue */
    --primary-dark: #0d47a1;        /* Darker blue */
    
    /* Secondary Colors */
    --secondary-color: #26a69a;     /* Teal */
    --secondary-light: #64d8cb;     /* Light teal */
    --secondary-dark: #00766c;      /* Dark teal */
    
    /* Accent Colors */
    --accent-color: #ff6d00;        /* Orange */
    --accent-light: #ff9e40;        /* Light orange */
    --accent-dark: #c43e00;         /* Dark orange */
    
    /* Neutral Colors */
    --light-color: #f8f9fa;         /* Almost white */
    --light-gray: #f1f3f4;          /* Very light gray */
    --medium-gray: #dadce0;         /* Medium gray */
    --dark-gray: #5f6368;           /* Dark gray */
    --darker-gray: #202124;         /* Almost black */
    
    /* Status Colors */
    --success-color: #0f9d58;       /* Green */
    --warning-color: #f4b400;       /* Yellow */
    --danger-color: #d93025;        /* Red */
    --info-color: #4285f4;          /* Blue */
    
    /* Shadows */
    --card-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --hover-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    --button-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    
    /* Transitions */
    --transition-speed: 0.3s;
    --transition-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Mode Variables */
.dark-mode {
    --light-color: #202124;
    --light-gray: #303134;
    --medium-gray: #5f6368;
    --dark-gray: #bdc1c6;
    --darker-gray: #e8eaed;
    
    --primary-color: #8ab4f8;
    --primary-light: #aecbfa;
    --primary-dark: #4285f4;
    
    --secondary-color: #81c995;
    --secondary-light: #a8dab5;
    --secondary-dark: #0f9d58;
    
    --accent-color: #fdd663;
    --accent-light: #fde293;
    --accent-dark: #f4b400;
}

/* General Styles */
body {
    font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-gray);
    color: var(--darker-gray);
    line-height: 1.6;
    transition: background-color var(--transition-speed) var(--transition-function),
                color var(--transition-speed) var(--transition-function);
}

/* RTL Adjustments */
[dir="rtl"] {
    text-align: right;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
    from { transform: scale(0.95); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes ripple {
    to { transform: scale(4); opacity: 0; }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s var(--transition-function) forwards;
}

.scale-in {
    animation: scaleIn 0.5s var(--transition-function) forwards;
}

.slide-in-right {
    animation: slideInRight 0.5s var(--transition-function) forwards;
}

.animate-chart {
    animation: scaleIn 0.7s var(--transition-function) forwards;
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

/* Navbar Styling */
.navbar {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.8rem 1rem;
    transition: background var(--transition-speed) var(--transition-function);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.4rem;
    color: white !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: all var(--transition-speed) var(--transition-function);
    position: relative;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.navbar-dark .navbar-nav .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
}

.navbar-dark .navbar-nav .nav-link.active:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background-color: var(--accent-color);
    border-radius: 3px 3px 0 0;
}

/* Dropdown Styling */
.dropdown-menu {
    border: none;
    box-shadow: var(--card-shadow);
    border-radius: 8px;
    background-color: white;
    border: 1px solid var(--medium-gray);
    padding: 0.5rem;
    transition: opacity var(--transition-speed) var(--transition-function),
                transform var(--transition-speed) var(--transition-function);
}

.dark-mode .dropdown-menu {
    background-color: var(--light-color);
    border-color: var(--medium-gray);
}

.dropdown-item {
    color: var(--darker-gray);
    transition: background-color var(--transition-speed) var(--transition-function);
    border-radius: 4px;
    margin: 0.1rem 0;
    padding: 0.5rem 1rem;
}

.dropdown-item:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.dropdown-item.active, .dropdown-item:active {
    background-color: var(--primary-color);
    color: white;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    transition: transform var(--transition-speed) var(--transition-function), 
                box-shadow var(--transition-speed) var(--transition-function),
                background-color var(--transition-speed) var(--transition-function);
    overflow: hidden;
    margin-bottom: 1.5rem;
    background-color: white;
    position: relative;
}

.dark-mode .card {
    background-color: var(--light-color);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(to bottom, rgba(255,255,255,0.1), transparent);
    transition: height 0.3s ease;
    z-index: 1;
    pointer-events: none;
}

.card:hover::before {
    height: 100%;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    font-weight: 600;
    padding: 1rem 1.25rem;
    border-bottom: none;
}

.card-body {
    padding: 1.5rem;
}

/* Dashboard Cards */
.dashboard-card {
    text-align: center;
    padding: 1.5rem;
    height: 100%;
    transition: all var(--transition-speed) var(--transition-function);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.dashboard-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.dashboard-card .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    transition: transform var(--transition-speed) var(--transition-function);
}

.dashboard-card:hover .icon {
    transform: scale(1.1) rotate(5deg);
}

.dashboard-card .count {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--darker-gray);
    margin-bottom: 0.5rem;
}

.dashboard-card .title {
    font-size: 1.2rem;
    color: var(--dark-gray);
    font-weight: 500;
}

/* Different card colors */
.dashboard-card.employees {
    background: linear-gradient(135deg, #e8f4fd, #d4e9fc);
}

.dashboard-card.employees:before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.dashboard-card.employees .icon {
    color: var(--primary-color);
}

.dashboard-card.allowances {
    background: linear-gradient(135deg, #e6f7ee, #d1f0e0);
}

.dashboard-card.allowances:before {
    background: linear-gradient(90deg, var(--success-color), #4ade80);
}

.dashboard-card.allowances .icon {
    color: var(--success-color);
}

.dashboard-card.promotions {
    background: linear-gradient(135deg, #fff4e6, #ffe8cc);
}

.dashboard-card.promotions:before {
    background: linear-gradient(90deg, var(--warning-color), #fcd34d);
}

.dashboard-card.promotions .icon {
    color: var(--warning-color);
}

.dashboard-card.appreciation {
    background: linear-gradient(135deg, #fde8e8, #fbd0d0);
}

.dashboard-card.appreciation:before {
    background: linear-gradient(90deg, var(--danger-color), #f87171);
}

.dashboard-card.appreciation .icon {
    color: var(--danger-color);
}

/* Table Styles */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    background-color: white;
    transition: box-shadow var(--transition-speed) var(--transition-function);
}

.table:hover {
    box-shadow: var(--hover-shadow);
}

.dark-mode .table {
    background-color: var(--light-color);
    color: var(--dark-gray);
}

.table th {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    padding: 1rem;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.table:hover th::after {
    transform: translateX(100%);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--medium-gray);
    transition: all var(--transition-speed) var(--transition-function);
}

.table tbody tr {
    transition: all var(--transition-speed) var(--transition-function);
    position: relative;
}

.table-hover tbody tr:hover {
    background-color: rgba(26, 115, 232, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    z-index: 1;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table-responsive {
    border-radius: 12px;
    overflow: hidden;
}

/* Status Indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-active {
    background-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(15, 157, 88, 0.2);
}

.status-pending {
    background-color: var(--warning-color);
    box-shadow: 0 0 0 3px rgba(244, 180, 0, 0.2);
}

.status-expired {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(217, 48, 37, 0.2);
}

.status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
}

.status-active {
    background-color: rgba(15, 157, 88, 0.15);
    color: var(--success-color);
}

.status-pending {
    background-color: rgba(244, 180, 0, 0.15);
    color: var(--warning-color);
}

.status-expired {
    background-color: rgba(217, 48, 37, 0.15);
    color: var(--danger-color);
}

/* Button Styling */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: all var(--transition-speed) var(--transition-function);
    position: relative;
    overflow: hidden;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
    z-index: 1;
    pointer-events: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn:hover::before {
    width: 300%;
    height: 300%;
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:active::before {
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.3);
    transition: all 0.1s ease;
}

.btn > * {
    position: relative;
    z-index: 2;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #00875a);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e09600);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #b7271e);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #3367d6);
    color: white;
}

.btn-light {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: var(--darker-gray);
}

.btn-dark {
    background: linear-gradient(135deg, #343a40, #212529);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
    box-shadow: none;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--button-shadow);
}

/* Form Styling */
.form-control {
    border-radius: 6px;
    padding: 0.75rem 1rem;
    border: 1px solid var(--medium-gray);
    transition: border-color var(--transition-speed) var(--transition-function), 
                box-shadow var(--transition-speed) var(--transition-function);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(26, 115, 232, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark-gray);
}

.form-select {
    border-radius: 6px;
    padding: 0.75rem 1rem;
    border: 1px solid var(--medium-gray);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Alert Styling */
.alert {
    border: none;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--card-shadow);
}

.alert-primary {
    background-color: rgba(26, 115, 232, 0.15);
    color: var(--primary-dark);
}

.alert-success {
    background-color: rgba(15, 157, 88, 0.15);
    color: var(--success-color);
}

.alert-warning {
    background-color: rgba(244, 180, 0, 0.15);
    color: var(--warning-color);
}

.alert-danger {
    background-color: rgba(217, 48, 37, 0.15);
    color: var(--danger-color);
}

.alert-info {
    background-color: rgba(66, 133, 244, 0.15);
    color: var(--info-color);
}

/* Badge Styling */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 500;
    border-radius: 50px;
}

.badge-primary {
    background-color: var(--primary-color);
    color: white;
}

.badge-success {
    background-color: var(--success-color);
    color: white;
}

.badge-warning {
    background-color: var(--warning-color);
    color: white;
}

.badge-danger {
    background-color: var(--danger-color);
    color: white;
}

.badge-info {
    background-color: var(--info-color);
    color: white;
}

/* Login Form */
.login-container {
    max-width: 400px;
    margin: 2rem auto;
    padding: 2rem;
    border-radius: 12px;
    background-color: white;
    box-shadow: var(--hover-shadow);
    border: 1px solid var(--medium-gray);
}

.dark-mode .login-container {
    background-color: var(--light-color);
    border-color: var(--medium-gray);
}

.login-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.login-logo img {
    max-width: 150px;
    height: auto;
}

.login-title {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--primary-color);
    font-weight: 700;
}

.login-form .form-control {
    margin-bottom: 1rem;
}

.login-form .btn {
    width: 100%;
    padding: 0.75rem;
    margin-top: 1rem;
}

/* Notification Styles */
.notification-counter {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translate(50%, -50%);
}

.notification-dropdown-menu {
    width: 320px;
    padding: 0;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--medium-gray);
    transition: background-color var(--transition-speed) var(--transition-function);
    cursor: pointer;
}

.notification-item:hover {
    background-color: var(--light-gray);
}

.notification-item.unread {
    background-color: rgba(26, 115, 232, 0.05);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(26, 115, 232, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.notification-content {
    flex: 1;
}

.notification-text {
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
}

/* Chart Styles */
.chart-container {
    position: relative;
    margin-bottom: 1.5rem;
    transition: all var(--transition-speed) var(--transition-function);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    background-color: white;
}

.dark-mode .chart-container {
    background-color: var(--light-color);
}

.chart-container:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    z-index: 5;
}

.chart-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 10;
    border-radius: 12px;
    backdrop-filter: blur(3px);
    transition: opacity 0.5s ease-out;
}

.dark-mode .chart-loading {
    background-color: rgba(30, 41, 59, 0.9);
}

.chart-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    border-right-color: var(--accent-color);
    animation: spin 1s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
    margin-bottom: 15px;
    box-shadow: 0 0 10px rgba(26, 115, 232, 0.3);
}

.dark-mode .chart-loading .spinner {
    border-color: rgba(255, 255, 255, 0.1);
    border-top-color: var(--primary-color);
    border-right-color: var(--accent-color);
    box-shadow: 0 0 10px rgba(138, 180, 248, 0.3);
}

.chart-loading .loading-text {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--primary-color);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s ease-out forwards;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .dashboard-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table th, .table td {
        padding: 0.75rem;
    }
    
    .dashboard-card .icon {
        font-size: 2.5rem;
    }
    
    .dashboard-card .count {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .dashboard-card .icon {
        font-size: 2rem;
    }
    
    .dashboard-card .count {
        font-size: 1.5rem;
    }
    
    .notification-dropdown-menu {
        width: 280px;
    }
}

/* Print Styles */
@media print {
    .navbar, .sidebar, .btn-print, .btn-export {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    body {
        background-color: white !important;
        color: black !important;
    }
}