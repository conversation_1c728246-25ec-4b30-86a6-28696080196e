<?php
/**
 * Minimal Add Employee Page - For Testing
 * صفحة إضافة موظف مبسطة جداً - للاختبار
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!-- Starting minimal add employee page -->\n";

// Include header
try {
    require_once 'includes/header.php';
    echo "<!-- Header loaded successfully -->\n";
} catch (Exception $e) {
    echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>خطأ</title></head><body>";
    echo "<h1>خطأ في تحميل header.php</h1>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</body></html>";
    exit;
}

// Simple authentication check
session_start();
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
    $_SESSION['username'] = 'test_admin';
    echo "<!-- Created test session -->\n";
}

echo "<!-- About to display content -->\n";
?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="display-5 mb-0">
                <i class="fas fa-user-plus me-2"></i> إضافة موظف جديد (اختبار)
            </h1>
        </div>
        <div class="col-md-6 text-md-end">
            <a href="employees.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى قائمة الموظفين
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h3>نموذج اختبار مبسط</h3>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h4>معلومات التشخيص:</h4>
                <ul>
                    <li>معرف المستخدم: <?php echo $_SESSION['user_id']; ?></li>
                    <li>دور المستخدم: <?php echo $_SESSION['role']; ?></li>
                    <li>إصدار PHP: <?php echo PHP_VERSION; ?></li>
                    <li>الوقت الحالي: <?php echo date('Y-m-d H:i:s'); ?></li>
                </ul>
            </div>

            <form method="post" class="needs-validation" novalidate>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="full_name" class="form-label">اسم الموظف <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                        <div class="invalid-feedback">يرجى إدخال اسم الموظف</div>
                    </div>
                    <div class="col-md-6">
                        <label for="hire_date" class="form-label">تاريخ التعيين <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="hire_date" name="hire_date" required>
                        <div class="invalid-feedback">يرجى إدخال تاريخ التعيين</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="current_grade" class="form-label">الدرجة الحالية <span class="text-danger">*</span></label>
                        <select class="form-select" id="current_grade" name="current_grade" required>
                            <option value="">اختر الدرجة</option>
                            <?php for ($i = 1; $i <= 10; $i++): ?>
                                <option value="<?php echo $i; ?>">الدرجة <?php echo $i; ?></option>
                            <?php endfor; ?>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار الدرجة الحالية</div>
                    </div>
                    <div class="col-md-4">
                        <label for="current_stage" class="form-label">المرحلة الحالية <span class="text-danger">*</span></label>
                        <select class="form-select" id="current_stage" name="current_stage" required>
                            <option value="">اختر المرحلة</option>
                            <?php for ($i = 1; $i <= 11; $i++): ?>
                                <option value="<?php echo $i; ?>">المرحلة <?php echo $i; ?></option>
                            <?php endfor; ?>
                        </select>
                        <div class="invalid-feedback">يرجى اختيار المرحلة الحالية</div>
                    </div>
                    <div class="col-md-4">
                        <label for="job_title" class="form-label">العنوان الوظيفي <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="job_title" name="job_title" required>
                        <div class="invalid-feedback">يرجى إدخال العنوان الوظيفي</div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-primary px-5">
                        <i class="fas fa-save me-1"></i> حفظ (اختبار)
                    </button>
                    <a href="employees.php" class="btn btn-secondary px-5">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </div>
            </form>

            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
                <div class="alert alert-success mt-4">
                    <h4>تم إرسال النموذج بنجاح!</h4>
                    <p><strong>البيانات المرسلة:</strong></p>
                    <pre><?php print_r($_POST); ?></pre>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Date Input Fix Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Minimal add employee page loaded');
    
    // Fix date inputs
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(function(input) {
        input.style.setProperty('direction', 'ltr', 'important');
        input.style.setProperty('text-align', 'left', 'important');
        input.setAttribute('dir', 'ltr');
        console.log('Fixed date input:', input.id);
    });
    
    console.log('Date inputs fixed:', dateInputs.length);
});
</script>

<?php
echo "<!-- About to include footer -->\n";
try {
    require_once 'includes/footer.php';
    echo "<!-- Footer loaded successfully -->\n";
} catch (Exception $e) {
    echo "<!-- Footer error: " . $e->getMessage() . " -->\n";
    echo "</body></html>";
}
?>

<!-- Debug: End of minimal add employee page -->
