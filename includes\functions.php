<?php
/**
 * Utility Functions
 * نظام إدارة العلاوات والترقية وكتب الشكر
 */

/**
 * Sanitize user input
 *
 * @param string $data Input data to sanitize
 * @return string Sanitized data
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Redirect to a specific page
 *
 * @param string $location URL to redirect to
 * @return void
 */
function redirect($location) {
    // Check if headers have already been sent
    if (!headers_sent()) {
        // If headers have not been sent, use header() for redirection
        header("Location: $location");
        exit;
    } else {
        // If headers have been sent, use JavaScript for redirection
        echo '<script type="text/javascript">';
        echo 'window.location.href="' . $location . '";';
        echo '</script>';
        echo '<noscript>';
        echo '<meta http-equiv="refresh" content="0;url=' . $location . '">';
        echo '</noscript>';
        exit;
    }
}

/**
 * Display flash message
 *
 * @param string $name Message name
 * @param string $message Message content
 * @param string $class CSS class for styling
 * @return void
 */
function flash($name = '', $message = '', $class = 'alert alert-success') {
    if (!empty($name)) {
        if (!empty($message) && empty($_SESSION[$name])) {
            $_SESSION[$name] = $message;
            $_SESSION[$name . '_class'] = $class;
        } else if (empty($message) && !empty($_SESSION[$name])) {
            $class = !empty($_SESSION[$name . '_class']) ? $_SESSION[$name . '_class'] : $class;
            echo '<div class="' . $class . '" id="msg-flash">' . $_SESSION[$name] . '</div>';
            unset($_SESSION[$name]);
            unset($_SESSION[$name . '_class']);
        }
    }
}

/**
 * Log system activity
 *
 * @param int $userId User ID
 * @param string $action Action performed
 * @param string $entityType Type of entity affected
 * @param int|null $entityId ID of entity affected
 * @param string|null $details Additional details
 * @return bool Success status
 */
function logActivity($userId, $action, $entityType, $entityId = null, $details = null) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            INSERT INTO system_logs (user_id, action, entity_type, entity_id, details, ip_address)
            VALUES (:user_id, :action, :entity_type, :entity_id, :details, :ip_address)
        ");

        $stmt->execute([
            ':user_id' => $userId,
            ':action' => $action,
            ':entity_type' => $entityType,
            ':entity_id' => $entityId,
            ':details' => $details,
            ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? null
        ]);

        return true;
    } catch (PDOException $e) {
        error_log("Log Activity Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Calculate next allowance date
 *
 * @param string $lastAllowanceDate Last allowance date (YYYY-MM-DD)
 * @param int $reductionMonths Months to reduce from standard 1 year
 * @return string Next allowance date (YYYY-MM-DD)
 */
function calculateNextAllowanceDate($lastAllowanceDate, $reductionMonths = 0) {
    $date = new DateTime($lastAllowanceDate);
    $date->add(new DateInterval('P1Y')); // Add 1 year

    if ($reductionMonths > 0) {
        $date->sub(new DateInterval('P' . $reductionMonths . 'M')); // Subtract months
    }

    return $date->format('Y-m-d');
}

/**
 * Calculate next promotion date
 *
 * @param string $lastPromotionDate Last promotion date (YYYY-MM-DD)
 * @param int $currentGrade Current grade
 * @param int $reductionMonths Months to reduce from standard years
 * @return string Next promotion date (YYYY-MM-DD)
 */
function calculateNextPromotionDate($lastPromotionDate, $currentGrade, $reductionMonths = 0) {
    global $pdo;
    $date = new DateTime($lastPromotionDate);

    // Get years needed for promotion from grade_service_years table
    try {
        $stmt = $pdo->prepare("
            SELECT service_years
            FROM grade_service_years
            WHERE grade_id = :grade_id
        ");
        $stmt->execute([':grade_id' => $currentGrade]);
        $result = $stmt->fetch();

        if ($result) {
            $yearsNeeded = (int)$result['service_years'];
        } else {
            // Fallback to constants if not found in table
            $yearsNeeded = ($currentGrade >= 5) ? PROMOTION_YEARS_LOWER_GRADES : PROMOTION_YEARS_UPPER_GRADES;
        }
    } catch (PDOException $e) {
        error_log("Get Service Years Error: " . $e->getMessage());
        // Fallback to constants if error
        $yearsNeeded = ($currentGrade >= 5) ? PROMOTION_YEARS_LOWER_GRADES : PROMOTION_YEARS_UPPER_GRADES;
    }

    $date->add(new DateInterval('P' . $yearsNeeded . 'Y')); // Add required years

    if ($reductionMonths > 0) {
        $date->sub(new DateInterval('P' . $reductionMonths . 'M')); // Subtract months
    }

    return $date->format('Y-m-d');
}

/**
 * Check if employee is eligible for allowance
 *
 * @param array $employee Employee data
 * @return bool Eligibility status
 */
function isEligibleForAllowance($employee) {
    // Check if employee has reached maximum allowances for current grade
    if ($employee['allowances_in_current_grade'] >= ALLOWANCES_PER_GRADE) {
        return false;
    }

    // Check if next allowance date has passed
    $today = new DateTime();
    $nextAllowanceDate = new DateTime($employee['next_allowance_date']);

    return $today >= $nextAllowanceDate;
}

/**
 * Check if employee is eligible for promotion
 *
 * @param array $employee Employee data
 * @return bool Eligibility status
 */
function isEligibleForPromotion($employee) {
    // Check education level restrictions
    $educationMaxGrade = getEducationMaxGrade($employee['education_level_id']);

    // If already at maximum grade for education level
    if ($employee['current_grade'] <= $educationMaxGrade) {
        return false;
    }

    // Special case for employees with 'إعدادية' education level
    if (isEducationLevel($employee['education_level_id'], 'إعدادية') &&
        $employee['current_grade'] == 3 &&
        $employee['years_of_service'] < 20) {
        return false;
    }

    // Special case for employees with 'دبلوم' education level
    if (isEducationLevel($employee['education_level_id'], 'دبلوم')) {
        if (($employee['current_grade'] == 3 && $employee['years_of_service'] < 20) ||
            ($employee['current_grade'] == 2 && $employee['years_of_service'] < 25)) {
            return false;
        }
    }

    // Check if next promotion date has passed
    $today = new DateTime();
    $nextPromotionDate = new DateTime($employee['next_promotion_date']);

    return $today >= $nextPromotionDate;
}

/**
 * Get maximum grade for education level
 *
 * @param int $educationLevelId Education level ID
 * @return int Maximum grade
 */
function getEducationMaxGrade($educationLevelId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT max_grade FROM education_levels WHERE id = :id");
        $stmt->execute([':id' => $educationLevelId]);
        $result = $stmt->fetch();

        return $result ? (int)$result['max_grade'] : 10;
    } catch (PDOException $e) {
        error_log("Get Education Max Grade Error: " . $e->getMessage());
        return 10; // Default to grade 10 if error
    }
}

/**
 * Check if employee has specific education level
 *
 * @param int $educationLevelId Education level ID
 * @param string $levelName Education level name to check
 * @return bool True if matches
 */
function isEducationLevel($educationLevelId, $levelName) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT id FROM education_levels WHERE id = :id AND name = :name");
        $stmt->execute([':id' => $educationLevelId, ':name' => $levelName]);

        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        error_log("Check Education Level Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Format date to Arabic format
 *
 * @param string $date Date in YYYY-MM-DD format
 * @return string Formatted date
 */
function formatArabicDate($date) {
    if (empty($date)) return '';

    $timestamp = strtotime($date);
    return date('d/m/Y', $timestamp);
}

/**
 * Calculate retirement date based on birth date
 *
 * @param string $birthDate Birth date in YYYY-MM-DD format
 * @return string Retirement date in YYYY-MM-DD format
 */
function calculateRetirementDate($birthDate) {
    if (empty($birthDate)) return null;

    $date = new DateTime($birthDate);
    $date->add(new DateInterval('P' . RETIREMENT_AGE . 'Y'));

    return $date->format('Y-m-d');
}

/**
 * Check if employee is eligible for early retirement
 *
 * @param array $employee Employee data
 * @return bool Eligibility status
 */
function isEligibleForEarlyRetirement($employee) {
    return $employee['years_of_service'] >= EARLY_RETIREMENT_YEARS;
}

/**
 * Get nominal salary based on grade and stage
 *
 * @param int $grade Employee grade
 * @param int $stage Employee stage
 * @return float|null Nominal salary or null if not found
 */
function getNominalSalary($grade, $stage) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT nominal_salary
            FROM stages
            WHERE grade_id = :grade AND stage_number = :stage
        ");
        $stmt->execute([':grade' => $grade, ':stage' => $stage]);
        $result = $stmt->fetch();

        return $result ? (float)$result['nominal_salary'] : null;
    } catch (PDOException $e) {
        error_log("Get Nominal Salary Error: " . $e->getMessage());
        return null;
    }
}

/**
 * Update employee's nominal salary
 *
 * @param int $employeeId Employee ID
 * @param int $grade Employee grade
 * @param int $stage Employee stage
 * @return bool Success status
 */
function updateEmployeeNominalSalary($employeeId, $grade, $stage) {
    global $pdo;

    $nominalSalary = getNominalSalary($grade, $stage);

    if ($nominalSalary === null) {
        return false;
    }

    try {
        $stmt = $pdo->prepare("
            UPDATE employees
            SET nominal_salary = :nominal_salary
            WHERE id = :id
        ");
        $stmt->execute([
            ':nominal_salary' => $nominalSalary,
            ':id' => $employeeId
        ]);

        return true;
    } catch (PDOException $e) {
        error_log("Update Employee Nominal Salary Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get job titles for a specific grade
 *
 * @param int $gradeId Grade ID
 * @return array Array of job titles
 */
function getJobTitlesForGrade($gradeId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT id, title, description
            FROM job_titles
            WHERE grade_id = :grade_id
            ORDER BY title ASC
        ");
        $stmt->execute([':grade_id' => $gradeId]);

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Get Job Titles Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Add salary history record
 *
 * @param int $employeeId Employee ID
 * @param int $grade Employee grade
 * @param int $stage Employee stage
 * @param float $nominalSalary Nominal salary
 * @param string $effectiveDate Effective date
 * @param string $reason Reason for salary change
 * @param string $notes Additional notes
 * @return bool Success status
 */
function addSalaryHistory($employeeId, $grade, $stage, $nominalSalary, $effectiveDate, $reason, $notes = null) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            INSERT INTO salary_history (
                employee_id, grade, stage, nominal_salary,
                effective_date, reason, notes, created_by
            ) VALUES (
                :employee_id, :grade, :stage, :nominal_salary,
                :effective_date, :reason, :notes, :created_by
            )
        ");

        $stmt->execute([
            ':employee_id' => $employeeId,
            ':grade' => $grade,
            ':stage' => $stage,
            ':nominal_salary' => $nominalSalary,
            ':effective_date' => $effectiveDate,
            ':reason' => $reason,
            ':notes' => $notes,
            ':created_by' => $_SESSION['user_id']
        ]);

        return true;
    } catch (PDOException $e) {
        error_log("Add Salary History Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Apply Law 103 for higher degree
 *
 * @param int $employeeId Employee ID
 * @param int $educationLevelId New education level ID
 * @param string $degreeDate Date of obtaining the degree
 * @param string $notes Additional notes
 * @return bool Success status
 */
function applyLaw103($employeeId, $educationLevelId, $degreeDate, $notes = null) {
    global $pdo;

    try {
        // Start transaction
        $pdo->beginTransaction();

        // Get employee data
        $stmt = $pdo->prepare("SELECT * FROM employees WHERE id = :id");
        $stmt->execute([':id' => $employeeId]);
        $employee = $stmt->fetch();

        if (!$employee) {
            throw new Exception("Employee not found");
        }

        // Get education level data
        $eduStmt = $pdo->prepare("SELECT * FROM education_levels WHERE id = :id");
        $eduStmt->execute([':id' => $educationLevelId]);
        $educationLevel = $eduStmt->fetch();

        if (!$educationLevel) {
            throw new Exception("Education level not found");
        }

        // Store previous grade
        $previousGrade = $employee['current_grade'];

        // Determine new grade based on education level
        $newGrade = $educationLevel['min_grade'];

        // Update employee record
        $updateStmt = $pdo->prepare("
            UPDATE employees SET
                education_level_id = :education_level_id,
                previous_grade = :previous_grade,
                current_grade = :current_grade,
                current_stage = 1,
                law_103_applied = 1,
                higher_degree_date = :higher_degree_date,
                max_grade_by_education = :max_grade_by_education
            WHERE id = :id
        ");

        $updateStmt->execute([
            ':education_level_id' => $educationLevelId,
            ':previous_grade' => $previousGrade,
            ':current_grade' => $newGrade,
            ':higher_degree_date' => $degreeDate,
            ':max_grade_by_education' => $educationLevel['max_grade'],
            ':id' => $employeeId
        ]);

        // Update nominal salary
        updateEmployeeNominalSalary($employeeId, $newGrade, 1);

        // Add higher degree history record
        $historyStmt = $pdo->prepare("
            INSERT INTO higher_degree_history (
                employee_id, education_level_id, previous_grade,
                new_grade, degree_date, notes, created_by
            ) VALUES (
                :employee_id, :education_level_id, :previous_grade,
                :new_grade, :degree_date, :notes, :created_by
            )
        ");

        $historyStmt->execute([
            ':employee_id' => $employeeId,
            ':education_level_id' => $educationLevelId,
            ':previous_grade' => $previousGrade,
            ':new_grade' => $newGrade,
            ':degree_date' => $degreeDate,
            ':notes' => $notes,
            ':created_by' => $_SESSION['user_id']
        ]);

        // Add salary history record
        $nominalSalary = getNominalSalary($newGrade, 1);
        addSalaryHistory(
            $employeeId,
            $newGrade,
            1,
            $nominalSalary,
            $degreeDate,
            'higher_degree',
            'تطبيق قانون 103 - الحصول على شهادة أعلى'
        );

        // Calculate next promotion date (2 years from degree date)
        $nextPromotionDate = new DateTime($degreeDate);
        $nextPromotionDate->add(new DateInterval('P' . LAW_103_PROMOTION_YEARS . 'Y'));

        // Update next promotion date
        $promotionStmt = $pdo->prepare("
            UPDATE employees SET
                next_promotion_date = :next_promotion_date
            WHERE id = :id
        ");

        $promotionStmt->execute([
            ':next_promotion_date' => $nextPromotionDate->format('Y-m-d'),
            ':id' => $employeeId
        ]);

        // Log activity
        logActivity(
            $_SESSION['user_id'],
            'تطبيق قانون 103',
            'employees',
            $employeeId,
            'تم تطبيق قانون 103 للموظف بسبب الحصول على شهادة أعلى'
        );

        // Commit transaction
        $pdo->commit();

        return true;
    } catch (Exception $e) {
        // Rollback transaction on error
        $pdo->rollBack();

        error_log("Apply Law 103 Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if employee has reached appreciation letters limit for the year
 *
 * @param int $employeeId Employee ID
 * @param int $year Year to check
 * @return bool True if limit reached
 */
function hasReachedAppreciationLettersLimit($employeeId, $year = null) {
    global $pdo;

    if ($year === null) {
        $year = date('Y');
    }

    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM appreciation_letters
            WHERE employee_id = :employee_id AND year = :year
        ");
        $stmt->execute([':employee_id' => $employeeId, ':year' => $year]);
        $result = $stmt->fetch();

        return $result['count'] >= MAX_APPRECIATION_LETTERS;
    } catch (PDOException $e) {
        error_log("Check Appreciation Letters Limit Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Create retirement alert
 *
 * @param int $employeeId Employee ID
 * @param string $retirementDate Retirement date
 * @return bool Success status
 */
function createRetirementAlert($employeeId, $retirementDate) {
    global $pdo;

    // Calculate alert date (1 year before retirement)
    $alertDate = new DateTime($retirementDate);
    $alertDate->sub(new DateInterval('P1Y'));

    // If alert date is in the past, don't create alert
    $today = new DateTime();
    if ($alertDate < $today) {
        return false;
    }

    try {
        $stmt = $pdo->prepare("
            INSERT INTO alerts (
                type, employee_id, alert_date, message
            ) VALUES (
                'retirement', :employee_id, :alert_date, :message
            )
        ");

        $stmt->execute([
            ':employee_id' => $employeeId,
            ':alert_date' => $alertDate->format('Y-m-d'),
            ':message' => 'الموظف سيصل إلى سن التقاعد في ' . formatArabicDate($retirementDate)
        ]);

        return true;
    } catch (PDOException $e) {
        error_log("Create Retirement Alert Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate unique employee number
 *
 * @return string Generated employee number
 */
function generateEmployeeNumber() {
    global $pdo;

    $year = date('Y');
    $attempts = 0;
    $maxAttempts = 100;

    do {
        // Generate a random 4-digit number
        $randomNumber = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        $employeeNumber = $year . $randomNumber;

        // Check if this number already exists
        try {
            $stmt = $pdo->prepare("SELECT id FROM employees WHERE employee_number = :employee_number");
            $stmt->execute([':employee_number' => $employeeNumber]);
            $exists = $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Generate Employee Number Error: " . $e->getMessage());
            // If there's an error, try a different number
            $exists = true;
        }

        $attempts++;
    } while ($exists && $attempts < $maxAttempts);

    if ($attempts >= $maxAttempts) {
        // Fallback to timestamp-based number if we can't find a unique random number
        $employeeNumber = $year . date('His');
    }

    return $employeeNumber;
}

/**
 * Calculate years of service based on hire date
 *
 * @param string $hireDate Hire date in YYYY-MM-DD format
 * @return int Years of service
 */
function calculateYearsOfService($hireDate) {
    if (empty($hireDate)) return 0;

    $hire = new DateTime($hireDate);
    $today = new DateTime();

    $interval = $hire->diff($today);
    return $interval->y;
}

/**
 * Get Arabic month name
 *
 * @param int $month Month number (1-12)
 * @return string Arabic month name
 */
function getArabicMonthName($month) {
    $arabicMonths = [
        1 => 'كانون الثاني',
        2 => 'شباط',
        3 => 'آذار',
        4 => 'نيسان',
        5 => 'أيار',
        6 => 'حزيران',
        7 => 'تموز',
        8 => 'آب',
        9 => 'أيلول',
        10 => 'تشرين الأول',
        11 => 'تشرين الثاني',
        12 => 'كانون الأول'
    ];

    return isset($arabicMonths[$month]) ? $arabicMonths[$month] : '';
}