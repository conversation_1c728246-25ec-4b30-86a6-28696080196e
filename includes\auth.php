<?php
/**
 * Authentication Functions
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

/**
 * Start session if not already started
 */
function startSession() {
    if (session_status() == PHP_SESSION_NONE) {
        // Include session configuration before starting the session
        $root_path = dirname(__DIR__);
        require_once $root_path . '/includes/session_config.php';
        session_start();
    }
}

/**
 * Check if user is logged in
 *
 * @return bool Login status
 */
function isLoggedIn() {
    startSession();
    return isset($_SESSION['user_id']);
}

/**
 * Get current user data
 *
 * @return array|null User data or null if not logged in
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT id, username, full_name, email, role FROM users WHERE id = :id");
        $stmt->execute([':id' => $_SESSION['user_id']]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Get Current User Error: " . $e->getMessage());
        return null;
    }
}

/**
 * Login user
 *
 * @param string $username Username
 * @param string $password Password
 * @return bool Login success
 */
function loginUser($username, $password) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT id, username, password, full_name, role FROM users WHERE username = :username");
        $stmt->execute([':username' => $username]);
        $user = $stmt->fetch();

        if ($user && password_verify($password, $user['password'])) {
            startSession();

            // Store user data in session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['role'] = $user['role'];

            // Log login activity
            logActivity($user['id'], 'تسجيل دخول', 'users', $user['id'], 'تم تسجيل الدخول بنجاح');

            return true;
        }

        return false;
    } catch (PDOException $e) {
        error_log("Login Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Logout user
 *
 * @return void
 */
function logoutUser() {
    startSession();

    // Log logout activity if user is logged in
    if (isset($_SESSION['user_id'])) {
        logActivity($_SESSION['user_id'], 'تسجيل خروج', 'users', $_SESSION['user_id'], 'تم تسجيل الخروج بنجاح');
    }

    // Unset all session variables
    $_SESSION = [];

    // Destroy the session
    session_destroy();
}

/**
 * Check if user has specific role
 *
 * @param string|array $roles Role(s) to check
 * @return bool Authorization status
 */
function hasRole($roles) {
    if (!isLoggedIn()) {
        return false;
    }

    // Convert single role to array
    if (!is_array($roles)) {
        $roles = [$roles];
    }

    return in_array($_SESSION['role'], $roles);
}

/**
 * Require login to access page
 *
 * @param string $redirectUrl URL to redirect if not logged in
 * @return void
 */
function requireLogin($redirectUrl = 'login.php') {
    if (!isLoggedIn()) {
        flash('login_message', 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة', 'alert alert-danger');
        redirect($redirectUrl);
    }
}

/**
 * Require specific role to access page
 *
 * @param string|array $roles Role(s) required
 * @param string $redirectUrl URL to redirect if not authorized
 * @return void
 */
function requireRole($roles, $redirectUrl = 'index.php') {
    requireLogin();

    if (!hasRole($roles)) {
        flash('auth_message', 'ليس لديك صلاحية للوصول إلى هذه الصفحة', 'alert alert-danger');
        redirect($redirectUrl);
    }
}

/**
 * Register new user
 *
 * @param string $username Username
 * @param string $password Password
 * @param string $fullName Full name
 * @param string $email Email
 * @param string $role User role
 * @return bool|string True on success, error message on failure
 */
function registerUser($username, $password, $fullName, $email, $role = 'viewer') {
    global $pdo;

    try {
        // Check if username already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = :username");
        $stmt->execute([':username' => $username]);

        if ($stmt->rowCount() > 0) {
            return "اسم المستخدم موجود بالفعل";
        }

        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Insert new user
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, full_name, email, role)
            VALUES (:username, :password, :full_name, :email, :role)
        ");

        $stmt->execute([
            ':username' => $username,
            ':password' => $hashedPassword,
            ':full_name' => $fullName,
            ':email' => $email,
            ':role' => $role
        ]);

        // Log activity
        $userId = $pdo->lastInsertId();
        logActivity(getCurrentUser()['id'] ?? $userId, 'إنشاء مستخدم', 'users', $userId, 'تم إنشاء مستخدم جديد');

        return true;
    } catch (PDOException $e) {
        error_log("Register User Error: " . $e->getMessage());
        return "حدث خطأ أثناء إنشاء المستخدم";
    }
}
