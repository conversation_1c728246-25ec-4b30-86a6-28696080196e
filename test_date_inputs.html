<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حقول التاريخ - Date Input Test</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        /* Test-specific styles */
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
        }
        
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        
        .test-pass {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .test-fail {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-calendar-alt me-2"></i>
                    اختبار حقول التاريخ - Date Input Test
                </h1>
                
                <!-- Test Section 1: Basic Date Inputs -->
                <div class="test-section">
                    <h3><i class="fas fa-vial me-2"></i>اختبار أساسي لحقول التاريخ</h3>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="test_hire_date" class="form-label">تاريخ التعيين</label>
                            <input type="date" class="form-control" id="test_hire_date" name="test_hire_date">
                            <div class="form-text">يجب أن يظهر النص من اليسار لليمين</div>
                        </div>
                        <div class="col-md-6">
                            <label for="test_birth_date" class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="test_birth_date" name="test_birth_date">
                            <div class="form-text">التنسيق: سنة-شهر-يوم</div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="test_allowance_date" class="form-label">تاريخ آخر علاوة</label>
                            <input type="date" class="form-control" id="test_allowance_date" name="test_allowance_date">
                        </div>
                        <div class="col-md-6">
                            <label for="test_promotion_date" class="form-label">تاريخ آخر ترفيع</label>
                            <input type="date" class="form-control" id="test_promotion_date" name="test_promotion_date">
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-primary" onclick="runBasicTest()">
                        <i class="fas fa-play me-1"></i>تشغيل الاختبار الأساسي
                    </button>
                    
                    <div id="basic-test-result" class="test-result" style="display: none;"></div>
                </div>
                
                <!-- Test Section 2: CSS Properties Test -->
                <div class="test-section">
                    <h3><i class="fas fa-code me-2"></i>اختبار خصائص CSS</h3>
                    
                    <button type="button" class="btn btn-info" onclick="runCSSTest()">
                        <i class="fas fa-search me-1"></i>فحص خصائص CSS
                    </button>
                    
                    <div id="css-test-result" class="test-result" style="display: none;"></div>
                </div>
                
                <!-- Test Section 3: Browser Compatibility -->
                <div class="test-section">
                    <h3><i class="fas fa-browser me-2"></i>معلومات المتصفح</h3>
                    
                    <button type="button" class="btn btn-secondary" onclick="showBrowserInfo()">
                        <i class="fas fa-info-circle me-1"></i>عرض معلومات المتصفح
                    </button>
                    
                    <div id="browser-info" class="debug-info" style="display: none;"></div>
                </div>
                
                <!-- Debug Console -->
                <div class="test-section">
                    <h3><i class="fas fa-terminal me-2"></i>وحدة التحكم</h3>
                    <div id="debug-console" class="debug-info">
                        جاري تحميل معلومات التشخيص...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Test Scripts -->
    <script>
        // Debug console
        function log(message) {
            const console = document.getElementById('debug-console');
            const timestamp = new Date().toLocaleTimeString();
            console.innerHTML += `[${timestamp}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }
        
        // Basic functionality test
        function runBasicTest() {
            log('بدء الاختبار الأساسي...');
            
            const dateInputs = document.querySelectorAll('input[type="date"]');
            const resultDiv = document.getElementById('basic-test-result');
            let results = [];
            
            dateInputs.forEach((input, index) => {
                const computedStyle = window.getComputedStyle(input);
                const direction = computedStyle.direction;
                const textAlign = computedStyle.textAlign;
                
                log(`حقل التاريخ ${index + 1} (${input.id}): direction=${direction}, text-align=${textAlign}`);
                
                const isCorrect = direction === 'ltr' && (textAlign === 'left' || textAlign === 'start');
                results.push({
                    id: input.id,
                    direction: direction,
                    textAlign: textAlign,
                    isCorrect: isCorrect
                });
            });
            
            // Display results
            let html = '<h5>نتائج الاختبار:</h5>';
            let allPassed = true;
            
            results.forEach(result => {
                const status = result.isCorrect ? 'نجح ✅' : 'فشل ❌';
                const className = result.isCorrect ? 'text-success' : 'text-danger';
                html += `<div class="${className}">
                    ${result.id}: ${status} (direction: ${result.direction}, text-align: ${result.textAlign})
                </div>`;
                if (!result.isCorrect) allPassed = false;
            });
            
            resultDiv.className = `test-result ${allPassed ? 'test-pass' : 'test-fail'}`;
            resultDiv.innerHTML = html;
            resultDiv.style.display = 'block';
            
            log(`الاختبار الأساسي ${allPassed ? 'نجح' : 'فشل'}`);
        }
        
        // CSS properties test
        function runCSSTest() {
            log('بدء اختبار CSS...');
            
            const testInput = document.getElementById('test_hire_date');
            const computedStyle = window.getComputedStyle(testInput);
            const resultDiv = document.getElementById('css-test-result');
            
            const properties = {
                'direction': computedStyle.direction,
                'text-align': computedStyle.textAlign,
                'unicode-bidi': computedStyle.unicodeBidi,
                'writing-mode': computedStyle.writingMode,
                'font-family': computedStyle.fontFamily
            };
            
            let html = '<h5>خصائص CSS المطبقة:</h5>';
            for (const [prop, value] of Object.entries(properties)) {
                html += `<div><strong>${prop}:</strong> ${value}</div>`;
                log(`CSS ${prop}: ${value}`);
            }
            
            resultDiv.className = 'test-result test-pass';
            resultDiv.innerHTML = html;
            resultDiv.style.display = 'block';
        }
        
        // Browser info
        function showBrowserInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'Language': navigator.language,
                'Platform': navigator.platform,
                'Viewport': `${window.innerWidth}x${window.innerHeight}`,
                'Screen': `${screen.width}x${screen.height}`,
                'RTL Support': document.dir === 'rtl' ? 'Yes' : 'No'
            };
            
            let html = '';
            for (const [key, value] of Object.entries(info)) {
                html += `<div><strong>${key}:</strong> ${value}</div>`;
            }
            
            document.getElementById('browser-info').innerHTML = html;
            document.getElementById('browser-info').style.display = 'block';
            
            log('تم عرض معلومات المتصفح');
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل الصفحة بنجاح');
            log('عدد حقول التاريخ الموجودة: ' + document.querySelectorAll('input[type="date"]').length);
            
            // Apply the same fixes as in add_employee.php
            const dateInputs = document.querySelectorAll('input[type="date"]');
            dateInputs.forEach(function(input) {
                input.style.setProperty('direction', 'ltr', 'important');
                input.style.setProperty('text-align', 'left', 'important');
                input.style.setProperty('unicode-bidi', 'embed', 'important');
                input.setAttribute('dir', 'ltr');
                input.classList.add('date-input-fixed');
                
                log(`تم إصلاح حقل التاريخ: ${input.id}`);
            });
            
            // Auto-run basic test after 1 second
            setTimeout(runBasicTest, 1000);
        });
    </script>
</body>
</html>
