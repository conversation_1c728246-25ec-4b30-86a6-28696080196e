<?php
/**
 * Simple Add Employee Page - For Testing
 * صفحة إضافة موظف مبسطة - للاختبار
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار صفحة إضافة موظف</title>";
echo "<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css'>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-4'>";
echo "<h1>اختبار صفحة إضافة موظف</h1>";
echo "<hr>";

try {
    echo "<p>1. تحميل الملفات المطلوبة...</p>";
    
    // Include required files step by step
    echo "<p>تحميل config...</p>";
    require_once 'config/config.php';
    echo "✅ تم تحميل config.php<br>";
    
    echo "<p>تحميل database...</p>";
    require_once 'config/database.php';
    echo "✅ تم تحميل database.php<br>";
    
    echo "<p>تحميل functions...</p>";
    require_once 'includes/functions.php';
    echo "✅ تم تحميل functions.php<br>";
    
    echo "<p>تحميل auth...</p>";
    require_once 'includes/auth.php';
    echo "✅ تم تحميل auth.php<br>";
    
    echo "<hr>";
    
    // Test database connection
    echo "<p>2. اختبار قاعدة البيانات...</p>";
    if (isset($pdo)) {
        echo "✅ اتصال قاعدة البيانات متاح<br>";
        
        // Test departments query
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM departments");
        $count = $stmt->fetch()['count'];
        echo "✅ عدد الأقسام: $count<br>";
        
        // Get departments for dropdown
        $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name");
        $departments = $deptStmt->fetchAll();
        echo "✅ تم جلب بيانات الأقسام<br>";
        
        // Get education levels
        $eduStmt = $pdo->query("SELECT id, name, max_grade FROM education_levels ORDER BY name");
        $educationLevels = $eduStmt->fetchAll();
        echo "✅ تم جلب بيانات المستويات التعليمية<br>";
        
    } else {
        echo "❌ اتصال قاعدة البيانات غير متاح<br>";
    }
    
    echo "<hr>";
    
    // Test authentication
    echo "<p>3. اختبار المصادقة...</p>";
    session_start();
    
    if (isset($_SESSION['user_id'])) {
        echo "✅ المستخدم مسجل دخول (ID: " . $_SESSION['user_id'] . ")<br>";
        echo "✅ دور المستخدم: " . ($_SESSION['role'] ?? 'غير محدد') . "<br>";
    } else {
        echo "⚠️ المستخدم غير مسجل دخول - سيتم إنشاء جلسة تجريبية<br>";
        // Create test session
        $_SESSION['user_id'] = 1;
        $_SESSION['role'] = 'admin';
        $_SESSION['username'] = 'test_admin';
        echo "✅ تم إنشاء جلسة تجريبية<br>";
    }
    
    echo "<hr>";
    
    // Display simple form
    echo "<p>4. عرض نموذج مبسط...</p>";
    
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<h3>نموذج إضافة موظف مبسط</h3>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    echo "<form method='post'>";
    
    echo "<div class='mb-3'>";
    echo "<label class='form-label'>اسم الموظف</label>";
    echo "<input type='text' class='form-control' name='full_name' required>";
    echo "</div>";
    
    echo "<div class='mb-3'>";
    echo "<label class='form-label'>القسم</label>";
    echo "<select class='form-select' name='department_id' required>";
    echo "<option value=''>اختر القسم</option>";
    if (isset($departments)) {
        foreach ($departments as $dept) {
            echo "<option value='{$dept['id']}'>{$dept['name']}</option>";
        }
    }
    echo "</select>";
    echo "</div>";
    
    echo "<div class='mb-3'>";
    echo "<label class='form-label'>المستوى التعليمي</label>";
    echo "<select class='form-select' name='education_level_id' required>";
    echo "<option value=''>اختر المستوى التعليمي</option>";
    if (isset($educationLevels)) {
        foreach ($educationLevels as $edu) {
            echo "<option value='{$edu['id']}'>{$edu['name']}</option>";
        }
    }
    echo "</select>";
    echo "</div>";
    
    echo "<div class='mb-3'>";
    echo "<label class='form-label'>تاريخ التعيين</label>";
    echo "<input type='date' class='form-control' name='hire_date' required>";
    echo "</div>";
    
    echo "<button type='submit' class='btn btn-primary'>حفظ (اختبار)</button>";
    echo "</form>";
    
    echo "</div>";
    echo "</div>";
    
    // Process form if submitted
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo "<hr>";
        echo "<div class='alert alert-info'>";
        echo "<h4>بيانات النموذج المرسلة:</h4>";
        echo "<pre>";
        print_r($_POST);
        echo "</pre>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>خطأ:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
