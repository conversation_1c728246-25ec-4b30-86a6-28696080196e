<?php
/**
 * Debug Add Employee Page
 * ملف تشخيص صفحة إضافة موظف
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>تشخيص صفحة إضافة موظف</h1>";
echo "<hr>";

// Test 1: Check if files exist
echo "<h2>1. فحص وجود الملفات المطلوبة:</h2>";

$requiredFiles = [
    'includes/header.php',
    'includes/footer.php', 
    'includes/functions.php',
    'includes/auth.php',
    'config/config.php',
    'config/database.php'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}

echo "<hr>";

// Test 2: Include header and check for errors
echo "<h2>2. اختبار تحميل header.php:</h2>";
try {
    require_once 'includes/header.php';
    echo "✅ تم تحميل header.php بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تحميل header.php: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Test 3: Check database connection
echo "<h2>3. اختبار اتصال قاعدة البيانات:</h2>";
try {
    if (isset($pdo)) {
        echo "✅ متغير PDO موجود<br>";
        
        // Test simple query
        $stmt = $pdo->query("SELECT 1");
        if ($stmt) {
            echo "✅ اتصال قاعدة البيانات يعمل<br>";
        } else {
            echo "❌ فشل في تنفيذ استعلام بسيط<br>";
        }
    } else {
        echo "❌ متغير PDO غير موجود<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Test 4: Check authentication
echo "<h2>4. اختبار المصادقة:</h2>";
try {
    if (function_exists('requireRole')) {
        echo "✅ دالة requireRole موجودة<br>";
        
        if (isset($_SESSION['user_id'])) {
            echo "✅ المستخدم مسجل دخول (ID: " . $_SESSION['user_id'] . ")<br>";
            
            if (isset($_SESSION['role'])) {
                echo "✅ دور المستخدم: " . $_SESSION['role'] . "<br>";
            } else {
                echo "⚠️ دور المستخدم غير محدد<br>";
            }
        } else {
            echo "❌ المستخدم غير مسجل دخول<br>";
        }
    } else {
        echo "❌ دالة requireRole غير موجودة<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في المصادقة: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Test 5: Check required functions
echo "<h2>5. اختبار الدوال المطلوبة:</h2>";

$requiredFunctions = [
    'generateEmployeeNumber',
    'calculateYearsOfService', 
    'calculateNextAllowanceDate',
    'calculateNextPromotionDate',
    'calculateRetirementDate',
    'getNominalSalary',
    'getEducationMaxGrade',
    'addSalaryHistory',
    'createRetirementAlert',
    'logActivity',
    'sanitize',
    'flash',
    'redirect'
];

foreach ($requiredFunctions as $func) {
    if (function_exists($func)) {
        echo "✅ $func موجودة<br>";
    } else {
        echo "❌ $func غير موجودة<br>";
    }
}

echo "<hr>";

// Test 6: Check required constants
echo "<h2>6. اختبار الثوابت المطلوبة:</h2>";

$requiredConstants = [
    'RETIREMENT_AGE',
    'PROMOTION_YEARS_LOWER_GRADES',
    'PROMOTION_YEARS_UPPER_GRADES'
];

foreach ($requiredConstants as $const) {
    if (defined($const)) {
        echo "✅ $const = " . constant($const) . "<br>";
    } else {
        echo "❌ $const غير معرف<br>";
    }
}

echo "<hr>";

// Test 7: Check database tables
echo "<h2>7. اختبار جداول قاعدة البيانات:</h2>";

$requiredTables = [
    'departments',
    'education_levels', 
    'employees',
    'job_titles',
    'stages',
    'system_logs',
    'alerts'
];

try {
    foreach ($requiredTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ جدول $table موجود<br>";
        } else {
            echo "⚠️ جدول $table غير موجود<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص الجداول: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Test 8: Test data retrieval
echo "<h2>8. اختبار جلب البيانات:</h2>";

try {
    // Test departments
    $deptStmt = $pdo->query("SELECT COUNT(*) as count FROM departments");
    $deptCount = $deptStmt->fetch()['count'];
    echo "✅ عدد الأقسام: $deptCount<br>";
    
    // Test education levels
    $eduStmt = $pdo->query("SELECT COUNT(*) as count FROM education_levels");
    $eduCount = $eduStmt->fetch()['count'];
    echo "✅ عدد المستويات التعليمية: $eduCount<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في جلب البيانات: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Test 9: PHP version and extensions
echo "<h2>9. معلومات PHP:</h2>";
echo "✅ إصدار PHP: " . PHP_VERSION . "<br>";
echo "✅ PDO متاح: " . (extension_loaded('pdo') ? 'نعم' : 'لا') . "<br>";
echo "✅ PDO MySQL متاح: " . (extension_loaded('pdo_mysql') ? 'نعم' : 'لا') . "<br>";

echo "<hr>";

// Test 10: Memory and execution
echo "<h2>10. معلومات الذاكرة والتنفيذ:</h2>";
echo "✅ حد الذاكرة: " . ini_get('memory_limit') . "<br>";
echo "✅ حد وقت التنفيذ: " . ini_get('max_execution_time') . " ثانية<br>";
echo "✅ استخدام الذاكرة الحالي: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB<br>";

echo "<hr>";

echo "<h2>خلاصة التشخيص:</h2>";
echo "<p>إذا كانت جميع الاختبارات تظهر ✅، فإن المشكلة قد تكون في:</p>";
echo "<ul>";
echo "<li>صلاحيات المستخدم (يجب أن يكون admin أو hr)</li>";
echo "<li>مشكلة في JavaScript أو CSS</li>";
echo "<li>مشكلة في cache المتصفح</li>";
echo "</ul>";

echo "<p><strong>للمتابعة:</strong></p>";
echo "<ol>";
echo "<li>تأكد من تسجيل الدخول بحساب admin أو hr</li>";
echo "<li>امسح cache المتصفح</li>";
echo "<li>افتح أدوات المطور (F12) وتحقق من وجود أخطاء</li>";
echo "<li>جرب الرابط: <a href='add_employee.php'>add_employee.php</a></li>";
echo "</ol>";

// Include footer if possible
try {
    require_once 'includes/footer.php';
} catch (Exception $e) {
    echo "<p>تعذر تحميل footer.php: " . $e->getMessage() . "</p>";
}
?>
