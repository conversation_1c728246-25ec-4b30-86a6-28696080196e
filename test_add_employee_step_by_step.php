<?php
/**
 * Step by Step Test for Add Employee Page
 * اختبار تدريجي لصفحة إضافة موظف
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>اختبار تدريجي لصفحة إضافة موظف</h1>";
echo "<hr>";

// Step 1: Test basic PHP
echo "<h2>الخطوة 1: اختبار PHP الأساسي</h2>";
echo "✅ PHP يعمل بشكل صحيح<br>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";
echo "<hr>";

// Step 2: Test file includes
echo "<h2>الخطوة 2: اختبار تحميل الملفات</h2>";

try {
    echo "تحميل config/config.php...<br>";
    require_once 'config/config.php';
    echo "✅ تم تحميل config.php<br>";
} catch (Exception $e) {
    echo "❌ خطأ في config.php: " . $e->getMessage() . "<br>";
    exit;
}

try {
    echo "تحميل config/database.php...<br>";
    require_once 'config/database.php';
    echo "✅ تم تحميل database.php<br>";
} catch (Exception $e) {
    echo "❌ خطأ في database.php: " . $e->getMessage() . "<br>";
    exit;
}

try {
    echo "تحميل includes/functions.php...<br>";
    require_once 'includes/functions.php';
    echo "✅ تم تحميل functions.php<br>";
} catch (Exception $e) {
    echo "❌ خطأ في functions.php: " . $e->getMessage() . "<br>";
    exit;
}

try {
    echo "تحميل includes/auth.php...<br>";
    require_once 'includes/auth.php';
    echo "✅ تم تحميل auth.php<br>";
} catch (Exception $e) {
    echo "❌ خطأ في auth.php: " . $e->getMessage() . "<br>";
    exit;
}

echo "<hr>";

// Step 3: Test database connection
echo "<h2>الخطوة 3: اختبار قاعدة البيانات</h2>";

if (isset($pdo)) {
    echo "✅ متغير PDO موجود<br>";
    
    try {
        $stmt = $pdo->query("SELECT 1");
        echo "✅ اتصال قاعدة البيانات يعمل<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في اتصال قاعدة البيانات: " . $e->getMessage() . "<br>";
        exit;
    }
} else {
    echo "❌ متغير PDO غير موجود<br>";
    exit;
}

echo "<hr>";

// Step 4: Test session and authentication
echo "<h2>الخطوة 4: اختبار الجلسة والمصادقة</h2>";

session_start();

if (!isset($_SESSION['user_id'])) {
    echo "⚠️ المستخدم غير مسجل دخول - إنشاء جلسة تجريبية<br>";
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
    $_SESSION['username'] = 'test_admin';
}

echo "✅ معرف المستخدم: " . $_SESSION['user_id'] . "<br>";
echo "✅ دور المستخدم: " . $_SESSION['role'] . "<br>";

echo "<hr>";

// Step 5: Test data retrieval
echo "<h2>الخطوة 5: اختبار جلب البيانات</h2>";

try {
    echo "جلب بيانات الأقسام...<br>";
    $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name ASC");
    $departments = $deptStmt->fetchAll();
    echo "✅ تم جلب " . count($departments) . " قسم<br>";
    
    echo "جلب بيانات المستويات التعليمية...<br>";
    $eduStmt = $pdo->query("SELECT id, name, max_grade FROM education_levels ORDER BY id ASC");
    $educationLevels = $eduStmt->fetchAll();
    echo "✅ تم جلب " . count($educationLevels) . " مستوى تعليمي<br>";
    
    echo "فحص جدول job_titles...<br>";
    $checkTableStmt = $pdo->query("SHOW TABLES LIKE 'job_titles'");
    $jobTitlesTableExists = ($checkTableStmt->rowCount() > 0);
    echo "✅ جدول job_titles " . ($jobTitlesTableExists ? "موجود" : "غير موجود") . "<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في جلب البيانات: " . $e->getMessage() . "<br>";
    exit;
}

echo "<hr>";

// Step 6: Test required functions
echo "<h2>الخطوة 6: اختبار الدوال المطلوبة</h2>";

$requiredFunctions = [
    'generateEmployeeNumber',
    'calculateYearsOfService',
    'calculateNextAllowanceDate',
    'calculateNextPromotionDate',
    'calculateRetirementDate',
    'getNominalSalary',
    'getEducationMaxGrade',
    'addSalaryHistory',
    'createRetirementAlert',
    'logActivity',
    'sanitize',
    'flash',
    'redirect'
];

$missingFunctions = [];
foreach ($requiredFunctions as $func) {
    if (function_exists($func)) {
        echo "✅ $func<br>";
    } else {
        echo "❌ $func غير موجودة<br>";
        $missingFunctions[] = $func;
    }
}

if (!empty($missingFunctions)) {
    echo "<div style='color: red;'>";
    echo "<strong>دوال مفقودة:</strong><br>";
    foreach ($missingFunctions as $func) {
        echo "- $func<br>";
    }
    echo "</div>";
    exit;
}

echo "<hr>";

// Step 7: Test constants
echo "<h2>الخطوة 7: اختبار الثوابت</h2>";

$requiredConstants = [
    'RETIREMENT_AGE',
    'PROMOTION_YEARS_LOWER_GRADES',
    'PROMOTION_YEARS_UPPER_GRADES'
];

foreach ($requiredConstants as $const) {
    if (defined($const)) {
        echo "✅ $const = " . constant($const) . "<br>";
    } else {
        echo "❌ $const غير معرف<br>";
    }
}

echo "<hr>";

// Step 8: Simulate the problematic part
echo "<h2>الخطوة 8: محاكاة الجزء المشكوك فيه</h2>";

try {
    echo "اختبار تنفيذ الكود الأساسي...<br>";
    
    // Simulate the exact code from add_employee.php
    $errors = [];
    
    // Test date validation (this was problematic before)
    $testDate = '2024-01-15';
    if (!empty($testDate) && !DateTime::createFromFormat('Y-m-d', $testDate)) {
        $errors[] = 'تنسيق التاريخ غير صحيح';
    }
    echo "✅ اختبار التاريخ نجح<br>";
    
    // Test employee number generation
    $employeeNumber = generateEmployeeNumber();
    echo "✅ تم توليد رقم موظف: $employeeNumber<br>";
    
    // Test years calculation
    $years = calculateYearsOfService('2020-01-01');
    echo "✅ حساب سنوات الخدمة: $years سنة<br>";
    
    echo "✅ جميع الاختبارات نجحت!<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في محاكاة الكود: " . $e->getMessage() . "<br>";
    echo "الملف: " . $e->getFile() . "<br>";
    echo "السطر: " . $e->getLine() . "<br>";
    exit;
}

echo "<hr>";

echo "<h2>النتيجة النهائية</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
echo "<h3>✅ جميع الاختبارات نجحت!</h3>";
echo "<p>يجب أن تعمل صفحة add_employee.php الآن بشكل صحيح.</p>";
echo "<p><a href='add_employee.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار صفحة إضافة موظف</a></p>";
echo "</div>";
?>
