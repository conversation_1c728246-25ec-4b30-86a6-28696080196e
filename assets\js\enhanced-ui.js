/**
 * Enhanced UI JavaScript
 * نظام إدارة العلاوات والترقية وكتب الشكر
 * Adds modern UI interactions and animations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize enhanced UI features
    initEnhancedUI();
});

/**
 * Initialize Enhanced UI Features
 */
function initEnhancedUI() {
    // Smooth scrolling with improved easing
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const targetPosition = target.getBoundingClientRect().top + window.pageYOffset;
                const startPosition = window.pageYOffset;
                const distance = targetPosition - startPosition;
                let startTime = null;
                const duration = 800;
                
                function animation(currentTime) {
                    if (startTime === null) startTime = currentTime;
                    const timeElapsed = currentTime - startTime;
                    const run = easeOutCubic(timeElapsed, startPosition, distance, duration);
                    window.scrollTo(0, run);
                    if (timeElapsed < duration) requestAnimationFrame(animation);
                }
                
                function easeOutCubic(t, b, c, d) {
                    t /= d;
                    t--;
                    return c * (t * t * t + 1) + b;
                }
                
                requestAnimationFrame(animation);
            }
        });
    });

    // Enhanced fade in cards with staggered animation
    document.querySelectorAll('.card').forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'opacity 0.6s ease-out, transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 + (index * 80));
    });

    // Advanced button hover effect
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2)';
        });
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.1)';
        });
    });

    // Enhanced ripple effect on button click
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height) * 2;
            ripple.style.width = ripple.style.height = `${size}px`;
            const x = e.clientX - rect.left - size/2;
            const y = e.clientY - rect.top - size/2;
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;
            ripple.style.animationDuration = '0.8s';
            setTimeout(() => {
                ripple.remove();
            }, 800);
        });
    });

    // Enhanced table row animations
    document.querySelectorAll('.table tbody tr').forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateX(-10px)';
        setTimeout(() => {
            row.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateX(0)';
        }, 100 + (index * 50));
    });

    // Dashboard card animations with improved effects
    document.querySelectorAll('.dashboard-card').forEach(card => {
        // Animate icon with bounce effect
        const icon = card.querySelector('.dashboard-card-icon');
        if (icon) {
            icon.style.transform = 'scale(0) rotate(-15deg)';
            setTimeout(() => {
                icon.style.transition = 'transform 0.7s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                icon.style.transform = 'scale(1) rotate(0)';
            }, 300);
        }

        // Animate counter with improved animation
        const count = card.querySelector('.dashboard-card-count');
        if (count) {
            const finalValue = parseInt(count.textContent, 10);
            count.textContent = '0';
            let currentValue = 0;
            const duration = 2000;
            const startTime = performance.now();
            
            function updateCount(currentTime) {
                const elapsedTime = currentTime - startTime;
                if (elapsedTime > duration) {
                    count.textContent = finalValue;
                    return;
                }
                
                const progress = elapsedTime / duration;
                // Use easeOutQuart for smoother animation
                const easedProgress = 1 - Math.pow(1 - progress, 4);
                currentValue = Math.floor(easedProgress * finalValue);
                count.textContent = currentValue;
                
                requestAnimationFrame(updateCount);
            }
            
            requestAnimationFrame(updateCount);
        }
    });

    // Initialize charts with improved colors and animations
    if (typeof Chart !== 'undefined') {
        Chart.defaults.font.family = "'Tajawal', 'Segoe UI', sans-serif";
        Chart.defaults.font.size = 14;
        Chart.defaults.color = '#555';
        Chart.defaults.plugins.title.font.size = 16;
        Chart.defaults.plugins.title.font.weight = 'bold';
        Chart.defaults.plugins.title.display = true;
        Chart.defaults.plugins.title.align = 'center';
        Chart.defaults.plugins.legend.position = 'bottom';
        Chart.defaults.plugins.tooltip.padding = 10;
        Chart.defaults.plugins.tooltip.boxPadding = 6;
        Chart.defaults.plugins.tooltip.cornerRadius = 6;
        Chart.defaults.plugins.tooltip.caretSize = 5;
        Chart.defaults.plugins.tooltip.displayColors = true;
        Chart.defaults.plugins.tooltip.usePointStyle = true;
        Chart.defaults.animation.duration = 2000;
        Chart.defaults.animation.easing = 'easeOutQuart';
        
        // Add custom animations for charts
        const originalDoughnutDraw = Chart.overrides.doughnut.plugins.legend.onClick;
        Chart.overrides.doughnut.plugins.legend.onClick = function(e, legendItem, legend) {
            const index = legendItem.index;
            const chart = legend.chart;
            
            // Add a small animation when toggling datasets
            chart.getDatasetMeta(0).data[index].options.circumference = 
                chart.getDatasetMeta(0).data[index].hidden ? 0 : Math.PI * 2;
            
            originalDoughnutDraw.call(this, e, legendItem, legend);
        };
    }

    // Initialize real-time notification system
    initNotifications();

    // Initialize theme toggle
    initThemeToggle();
}

/**
 * Initialize animations for page elements
 */
function initAnimations() {
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.classList.add('fade-in');
        card.style.animationDelay = `${index * 0.1}s`;
    });
    
    // Add hover effects to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Add ripple effect to buttons
    buttons.forEach(button => {
        button.addEventListener('click', createRipple);
    });
}

/**
 * Create ripple effect on button click
 */
function createRipple(event) {
    const button = event.currentTarget;
    
    const circle = document.createElement('span');
    const diameter = Math.max(button.clientWidth, button.clientHeight);
    const radius = diameter / 2;
    
    circle.style.width = circle.style.height = `${diameter}px`;
    circle.style.left = `${event.clientX - button.offsetLeft - radius}px`;
    circle.style.top = `${event.clientY - button.offsetTop - radius}px`;
    circle.classList.add('ripple');
    
    const ripple = button.querySelector('.ripple');
    if (ripple) {
        ripple.remove();
    }
    
    button.appendChild(circle);
}

/**
 * Initialize dashboard card animations and counters
 */
function initDashboardCards() {
    const dashboardCards = document.querySelectorAll('.dashboard-card');
    
    // Add hover effect to dashboard cards
    dashboardCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.icon');
            if (icon) {
                icon.style.transform = 'scale(1.2) rotate(5deg)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0)';
            }
        });
    });
    
    // Animate counters
    const counters = document.querySelectorAll('.dashboard-card .count');
    counters.forEach(counter => {
        const target = parseInt(counter.innerText, 10);
        let count = 0;
        const duration = 1500; // ms
        const frameDuration = 1000 / 60; // 60fps
        const totalFrames = Math.round(duration / frameDuration);
        const increment = target / totalFrames;
        
        // Save original text
        const originalText = counter.innerText;
        
        // Only animate if it's a number and reasonable to animate
        if (!isNaN(target) && target > 0 && target < 1000) {
            counter.innerText = '0';
            
            const timer = setInterval(() => {
                count += increment;
                if (count >= target) {
                    clearInterval(timer);
                    counter.innerText = originalText;
                } else {
                    counter.innerText = Math.floor(count).toString();
                }
            }, frameDuration);
        }
    });
}

/**
 * Initialize charts with better colors and animations
 */
function initCharts() {
    // Check if Chart.js is available
    if (typeof Chart !== 'undefined') {
        // Set default chart options
        Chart.defaults.font.family = "'Tajawal', 'Segoe UI', sans-serif";
        Chart.defaults.color = '#2c3e50';
        Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(44, 62, 80, 0.8)';
        
        // Get all canvas elements for charts
        const chartCanvases = document.querySelectorAll('canvas[id^="chart"]');
        
        // Add animation when charts come into view
        const observerOptions = {
            root: null,
            rootMargin: '0px',
            threshold: 0.1
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-chart');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        chartCanvases.forEach(canvas => {
            observer.observe(canvas);
        });
    }
}

/**
 * Initialize notifications system
 */
function initNotifications() {
    // Check for new notifications every 60 seconds
    setInterval(checkNewNotifications, 60000);
    
    // Initialize notification dropdown
    const notificationDropdown = document.getElementById('notificationsDropdown');
    if (notificationDropdown) {
        notificationDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            loadNotifications();
        });
    }
    
    // Mark notifications as read when clicked
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('notification-item')) {
            const notificationId = e.target.dataset.id;
            if (notificationId) {
                markNotificationAsRead(notificationId);
            }
        }
    });
    
    // Initial check for notifications
    checkNewNotifications();
}

/**
 * Check for new notifications
 */
function checkNewNotifications() {
    fetch('api/notifications/get_new_notifications.php')
        .then(response => response.json())
        .then(data => {
            updateNotificationBadge(data.count);
        })
        .catch(error => console.error('Error checking notifications:', error));
}

/**
 * Load notifications into dropdown
 */
function loadNotifications() {
    fetch('api/notifications/get_notifications.php?limit=5')
        .then(response => response.json())
        .then(data => {
            displayNotifications(data.notifications);
        })
        .catch(error => console.error('Error loading notifications:', error));
}

/**
 * Update notification badge count
 */
function updateNotificationBadge(count) {
    const badge = document.querySelector('.notification-counter');
    if (badge) {
        if (count > 0) {
            badge.textContent = count;
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }
}

/**
 * Display notifications in dropdown
 */
function displayNotifications(notifications) {
    const container = document.querySelector('.notification-dropdown-menu');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (notifications.length === 0) {
        container.innerHTML = '<div class="p-3 text-center">لا توجد إشعارات جديدة</div>';
        return;
    }
    
    notifications.forEach(notification => {
        const item = document.createElement('div');
        item.className = `notification-item ${notification.is_read ? '' : 'unread'}`;
        item.dataset.id = notification.id;
        
        item.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="notification-icon me-3">
                    <i class="fas ${getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-text">${notification.message}</div>
                    <div class="notification-time text-muted small">${notification.created_at}</div>
                </div>
            </div>
        `;
        
        container.appendChild(item);
    });
    
    // Add link to all notifications
    const viewAll = document.createElement('div');
    viewAll.className = 'text-center p-2 border-top';
    viewAll.innerHTML = '<a href="notifications.php" class="btn btn-sm btn-link">عرض جميع الإشعارات</a>';
    container.appendChild(viewAll);
}

/**
 * Get appropriate icon for notification type
 */
function getNotificationIcon(type) {
    switch (type) {
        case 'allowance': return 'fa-money-bill-wave';
        case 'promotion': return 'fa-level-up-alt';
        case 'appreciation': return 'fa-certificate';
        case 'retirement': return 'fa-user-clock';
        case 'system': return 'fa-cog';
        default: return 'fa-bell';
    }
}

/**
 * Mark notification as read
 */
function markNotificationAsRead(id) {
    fetch(`api/notifications/mark_as_read.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const item = document.querySelector(`.notification-item[data-id="${id}"]`);
                if (item) {
                    item.classList.remove('unread');
                }
                checkNewNotifications(); // Update badge count
            }
        })
        .catch(error => console.error('Error marking notification as read:', error));
}

/**
 * Initialize theme toggle functionality
 */
function initThemeToggle() {
    // Create theme toggle button if it doesn't exist
    if (!document.getElementById('theme-toggle')) {
        const navbar = document.querySelector('.navbar-nav');
        if (navbar) {
            const themeToggle = document.createElement('li');
            themeToggle.className = 'nav-item';
            themeToggle.innerHTML = `
                <a class="nav-link" href="#" id="theme-toggle">
                    <i class="fas fa-moon"></i>
                </a>
            `;
            navbar.appendChild(themeToggle);
            
            // Add event listener to toggle theme
            document.getElementById('theme-toggle').addEventListener('click', function(e) {
                e.preventDefault();
                toggleDarkMode();
            });
            
            // Check for saved theme preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-mode');
                updateThemeIcon(true);
            }
        }
    }
}

/**
 * Toggle dark mode
 */
function toggleDarkMode() {
    const isDarkMode = document.body.classList.toggle('dark-mode');
    localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');
    updateThemeIcon(isDarkMode);
}

/**
 * Update theme toggle icon
 */
function updateThemeIcon(isDarkMode) {
    const themeIcon = document.querySelector('#theme-toggle i');
    if (themeIcon) {
        themeIcon.className = isDarkMode ? 'fas fa-sun' : 'fas fa-moon';
    }
}