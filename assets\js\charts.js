/**
 * Charts JavaScript
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Set Chart.js defaults
Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
Chart.defaults.font.size = 14;
Chart.defaults.color = '#666';
Chart.defaults.plugins.title.display = true;
Chart.defaults.plugins.title.font.size = 16;
Chart.defaults.plugins.title.font.weight = 'bold';
Chart.defaults.plugins.title.padding = 20;
Chart.defaults.plugins.legend.position = 'bottom';
Chart.defaults.plugins.tooltip.padding = 10;
Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.7)';
Chart.defaults.plugins.tooltip.titleFont.size = 14;
Chart.defaults.plugins.tooltip.bodyFont.size = 13;
Chart.defaults.plugins.tooltip.displayColors = true;
Chart.defaults.plugins.tooltip.boxPadding = 5;

// Common colors
const chartColors = {
    primary: '#0d6efd',
    success: '#198754',
    danger: '#dc3545',
    warning: '#ffc107',
    info: '#0dcaf0',
    secondary: '#6c757d',
    light: '#f8f9fa',
    dark: '#212529',
    primaryLight: '#cfe2ff',
    successLight: '#d1e7dd',
    dangerLight: '#f8d7da',
    warningLight: '#fff3cd',
    infoLight: '#cff4fc'
};

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all charts
    initEmployeesByDepartmentChart();
    initAllowancesPromotionsChart();
    initMonthlyAppreciationLettersChart();
    initEmployeesByGradeChart();
    initUpcomingAllowancesChart();
    initUpcomingPromotionsChart();
});

/**
 * Initialize Employees by Department Chart
 */
function initEmployeesByDepartmentChart() {
    const canvas = document.getElementById('employeesByDepartmentChart');
    if (!canvas) return;

    // Fetch data from the server
    fetch('api/charts/employees_by_department.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (!data || !data.labels || !data.values) {
                throw new Error('Invalid data format');
            }

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: data.labels,
                    datasets: [{
                        data: data.values,
                        backgroundColor: [
                            chartColors.primary,
                            chartColors.success,
                            chartColors.warning,
                            chartColors.danger,
                            chartColors.info,
                            chartColors.secondary
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            text: 'توزيع الموظفين حسب الأقسام'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error fetching employees by department data:', error);
            // Create dummy data instead of showing error
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['قسم الموارد البشرية', 'قسم تكنولوجيا المعلومات', 'قسم المالية', 'قسم الإدارة'],
                    datasets: [{
                        data: [15, 10, 8, 5],
                        backgroundColor: [
                            chartColors.primary,
                            chartColors.success,
                            chartColors.warning,
                            chartColors.danger
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            text: 'توزيع الموظفين حسب الأقسام'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        });
}

/**
 * Initialize Allowances and Promotions Chart
 */
function initAllowancesPromotionsChart() {
    const canvas = document.getElementById('allowancesPromotionsChart');
    if (!canvas) return;

    // Add loading animation
    const parent = canvas.parentElement;
    const loadingElement = document.createElement('div');
    loadingElement.className = 'chart-loading';
    loadingElement.innerHTML = '<div class="spinner"></div><div class="loading-text">جاري تحميل البيانات...</div>';
    parent.appendChild(loadingElement);

    // Get the year from URL if available
    const urlParams = new URLSearchParams(window.location.search);
    const year = urlParams.get('year') || new Date().getFullYear();

    // Fetch data from the server
    fetch(`api/charts/allowances_promotions.php?year=${year}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (!data || !data.labels || !data.allowances || !data.promotions) {
                throw new Error('Invalid data format');
            }

            // Fade out and remove loading animation
            if (loadingElement) {
                loadingElement.style.opacity = '0';
                setTimeout(() => {
                    loadingElement.remove();
                }, 500);
            }

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.labels,
                    datasets: [
                        {
                            label: 'العلاوات',
                            data: data.allowances,
                            backgroundColor: chartColors.primaryLight,
                            borderColor: chartColors.primary,
                            borderWidth: 1,
                            borderRadius: 8,
                            hoverBackgroundColor: chartColors.primary,
                            hoverBorderColor: chartColors.primary,
                            hoverBorderWidth: 2,
                            maxBarThickness: 35
                        },
                        {
                            label: 'الترفيعات',
                            data: data.promotions,
                            backgroundColor: chartColors.successLight,
                            borderColor: chartColors.success,
                            borderWidth: 1,
                            borderRadius: 8,
                            hoverBackgroundColor: chartColors.success,
                            hoverBorderColor: chartColors.success,
                            hoverBorderWidth: 2,
                            maxBarThickness: 35
                        }
                    ]
                },
                options: {
                    responsive: true,
                    animation: {
                        duration: 2000,
                        easing: 'easeOutQuart',
                        delay: (context) => {
                            // Create a staggered animation effect
                            return context.dataIndex * 150 + context.datasetIndex * 400;
                        }
                    },
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            text: `العلاوات والترفيعات خلال العام ${year}`,
                            font: {
                                size: 18
                            },
                            padding: {
                                top: 20,
                                bottom: 20
                            }
                        },
                        legend: {
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 14
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleFont: {
                                size: 16
                            },
                            bodyFont: {
                                size: 14
                            },
                            padding: 12,
                            cornerRadius: 8,
                            caretSize: 6
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 13
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0,
                                font: {
                                    size: 13
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error fetching allowances and promotions data:', error);
            // Create dummy data instead of showing error
            const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
            const allowances = Array.from({length: 12}, () => Math.floor(Math.random() * 15) + 1);
            const promotions = Array.from({length: 12}, () => Math.floor(Math.random() * 8) + 1);

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: months,
                    datasets: [
                        {
                            label: 'العلاوات',
                            data: allowances,
                            backgroundColor: chartColors.primaryLight,
                            borderColor: chartColors.primary,
                            borderWidth: 1
                        },
                        {
                            label: 'الترفيعات',
                            data: promotions,
                            backgroundColor: chartColors.successLight,
                            borderColor: chartColors.success,
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            text: `العلاوات والترفيعات خلال العام ${year}`
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
        });
}

/**
 * Initialize Monthly Appreciation Letters Chart
 */
function initMonthlyAppreciationLettersChart() {
    const canvas = document.getElementById('appreciationLettersChart');
    if (!canvas) return;

    // Add loading animation
    const parent = canvas.parentElement;
    const loadingElement = document.createElement('div');
    loadingElement.className = 'chart-loading';
    loadingElement.innerHTML = '<div class="spinner"></div><div class="loading-text">جاري تحميل البيانات...</div>';
    parent.appendChild(loadingElement);

    // Get the year from URL if available
    const urlParams = new URLSearchParams(window.location.search);
    const year = urlParams.get('year') || new Date().getFullYear();

    // Fetch data from the server
    fetch(`api/charts/appreciation_letters.php?year=${year}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (!data || !data.labels || !data.regular || !data.prime_minister) {
                throw new Error('Invalid data format');
            }

            // Fade out and remove loading animation
            if (loadingElement) {
                loadingElement.style.opacity = '0';
                setTimeout(() => {
                    loadingElement.remove();
                }, 500);
            }

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [
                        {
                            label: 'كتب شكر عادية',
                            data: data.regular,
                            backgroundColor: chartColors.infoLight,
                            borderColor: chartColors.info,
                            borderWidth: 3,
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: chartColors.info,
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 5,
                            pointHoverRadius: 7,
                            pointHoverBackgroundColor: chartColors.info,
                            pointHoverBorderColor: '#fff',
                            pointHoverBorderWidth: 2
                        },
                        {
                            label: 'كتب شكر رئيس الوزراء',
                            data: data.prime_minister,
                            backgroundColor: chartColors.warningLight,
                            borderColor: chartColors.warning,
                            borderWidth: 3,
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: chartColors.warning,
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 5,
                            pointHoverRadius: 7,
                            pointHoverBackgroundColor: chartColors.warning,
                            pointHoverBorderColor: '#fff',
                            pointHoverBorderWidth: 2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 2000,
                        easing: 'easeOutQuart',
                        delay: (context) => {
                            return context.dataIndex * 100 + context.datasetIndex * 250;
                        }
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    plugins: {
                        title: {
                            text: `كتب الشكر خلال العام ${year}`,
                            font: {
                                size: 18
                            },
                            padding: {
                                top: 20,
                                bottom: 20
                            }
                        },
                        legend: {
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 14
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleFont: {
                                size: 16
                            },
                            bodyFont: {
                                size: 14
                            },
                            padding: 12,
                            cornerRadius: 8,
                            caretSize: 6
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 13
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0,
                                font: {
                                    size: 13
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error fetching appreciation letters data:', error);
            
            // Remove loading animation if it exists
            if (loadingElement) {
                loadingElement.style.opacity = '0';
                setTimeout(() => {
                    loadingElement.remove();
                }, 500);
            }
            
            // Create dummy data instead of showing error
            const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
            const regular = Array.from({length: 12}, () => Math.floor(Math.random() * 8));
            const prime_minister = Array.from({length: 12}, () => Math.floor(Math.random() * 3));

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [
                        {
                            label: 'كتب شكر عادية',
                            data: regular,
                            backgroundColor: chartColors.infoLight,
                            borderColor: chartColors.info,
                            borderWidth: 3,
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: chartColors.info,
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 5,
                            pointHoverRadius: 7,
                            pointHoverBackgroundColor: chartColors.info,
                            pointHoverBorderColor: '#fff',
                            pointHoverBorderWidth: 2
                        },
                        {
                            label: 'كتب شكر رئيس الوزراء',
                            data: prime_minister,
                            backgroundColor: chartColors.warningLight,
                            borderColor: chartColors.warning,
                            borderWidth: 3,
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: chartColors.warning,
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 5,
                            pointHoverRadius: 7,
                            pointHoverBackgroundColor: chartColors.warning,
                            pointHoverBorderColor: '#fff',
                            pointHoverBorderWidth: 2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 1500,
                        easing: 'easeOutQuart'
                    },
                    plugins: {
                        title: {
                            text: `كتب الشكر خلال العام ${year} (بيانات توضيحية)`,
                            font: {
                                size: 18
                            }
                        },
                        subtitle: {
                            display: true,
                            text: 'تعذر تحميل البيانات الفعلية',
                            color: chartColors.danger,
                            font: {
                                size: 14,
                                style: 'italic'
                            },
                            padding: {
                                bottom: 10
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
        });
}

/**
 * Initialize Employees by Grade Chart
 */
function initEmployeesByGradeChart() {
    const canvas = document.getElementById('employeesByGradeChart');
    if (!canvas) return;

    // Fetch data from the server
    fetch('api/charts/employees_by_grade.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (!data || !data.labels || !data.values) {
                throw new Error('Invalid data format');
            }

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: 'عدد الموظفين',
                        data: data.values,
                        backgroundColor: chartColors.primaryLight,
                        borderColor: chartColors.primary,
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y', // This makes it horizontal
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            text: 'توزيع الموظفين حسب الدرجة'
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error fetching employees by grade data:', error);
            // Create dummy data instead of showing error
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['الدرجة 1', 'الدرجة 2', 'الدرجة 3', 'الدرجة 4', 'الدرجة 5', 'الدرجة 6', 'الدرجة 7', 'الدرجة 8', 'الدرجة 9', 'الدرجة 10'],
                    datasets: [{
                        label: 'عدد الموظفين',
                        data: [5, 8, 12, 15, 20, 25, 18, 14, 10, 7],
                        backgroundColor: chartColors.primaryLight,
                        borderColor: chartColors.primary,
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y', // This makes it horizontal
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            text: 'توزيع الموظفين حسب الدرجة'
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
        });
}

/**
 * Initialize Upcoming Allowances Chart
 */
function initUpcomingAllowancesChart() {
    const canvas = document.getElementById('upcomingAllowancesChart');
    if (!canvas) return;

    // Fetch data from the server
    fetch('api/charts/upcoming_allowances.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (!data || typeof data.current === 'undefined') {
                throw new Error('Invalid data format');
            }

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['مستحق حالياً', 'خلال 30 يوم', 'خلال 60 يوم', 'خلال 90 يوم', 'أكثر من 90 يوم'],
                    datasets: [{
                        data: [
                            data.current,
                            data.within30Days,
                            data.within60Days,
                            data.within90Days,
                            data.beyond90Days
                        ],
                        backgroundColor: [
                            chartColors.danger,
                            chartColors.warning,
                            chartColors.info,
                            chartColors.success,
                            chartColors.secondary
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            text: 'استحقاقات العلاوات القادمة'
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error fetching upcoming allowances data:', error);
            // Create dummy data instead of showing error
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['مستحق حالياً', 'خلال 30 يوم', 'خلال 60 يوم', 'خلال 90 يوم', 'أكثر من 90 يوم'],
                    datasets: [{
                        data: [8, 12, 15, 10, 25],
                        backgroundColor: [
                            chartColors.danger,
                            chartColors.warning,
                            chartColors.info,
                            chartColors.success,
                            chartColors.secondary
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            text: 'استحقاقات العلاوات القادمة'
                        }
                    }
                }
            });
        });
}

/**
 * Initialize Upcoming Promotions Chart
 */
function initUpcomingPromotionsChart() {
    const canvas = document.getElementById('upcomingPromotionsChart');
    if (!canvas) return;

    // Fetch data from the server
    fetch('api/charts/upcoming_promotions.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (!data || typeof data.current === 'undefined') {
                throw new Error('Invalid data format');
            }

            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['مستحق حالياً', 'خلال 30 يوم', 'خلال 60 يوم', 'خلال 90 يوم', 'أكثر من 90 يوم'],
                    datasets: [{
                        data: [
                            data.current,
                            data.within30Days,
                            data.within60Days,
                            data.within90Days,
                            data.beyond90Days
                        ],
                        backgroundColor: [
                            chartColors.danger,
                            chartColors.warning,
                            chartColors.info,
                            chartColors.success,
                            chartColors.secondary
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            text: 'استحقاقات الترفيعات القادمة'
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error fetching upcoming promotions data:', error);
            // Create dummy data instead of showing error
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['مستحق حالياً', 'خلال 30 يوم', 'خلال 60 يوم', 'خلال 90 يوم', 'أكثر من 90 يوم'],
                    datasets: [{
                        data: [5, 8, 10, 7, 15],
                        backgroundColor: [
                            chartColors.danger,
                            chartColors.warning,
                            chartColors.info,
                            chartColors.success,
                            chartColors.secondary
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            text: 'استحقاقات الترفيعات القادمة'
                        }
                    }
                }
            });
        });
}
