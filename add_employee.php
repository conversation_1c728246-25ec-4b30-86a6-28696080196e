<?php
/**
 * Add Employee Page
 * نظام إدارة العلاوات والترفيع وكتب الشكر
 */

// Include header
require_once 'includes/header.php';

// Require login and proper role
requireRole(['admin', 'hr']);

// Get departments and education levels
try {
    $deptStmt = $pdo->query("SELECT id, name FROM departments ORDER BY name ASC");
    $departments = $deptStmt->fetchAll();

    $eduStmt = $pdo->query("SELECT id, name, max_grade FROM education_levels ORDER BY id ASC");
    $educationLevels = $eduStmt->fetchAll();

    // Check if job_titles table exists
    $checkTableStmt = $pdo->query("SHOW TABLES LIKE 'job_titles'");
    $jobTitlesTableExists = ($checkTableStmt->rowCount() > 0);

    // Get job titles for each grade
    $jobTitlesByGrade = [];
    if ($jobTitlesTableExists) {
        $jobTitlesStmt = $pdo->query("
            SELECT id, grade_id, title, description
            FROM job_titles
            ORDER BY grade_id ASC, title ASC
        ");
        $allJobTitles = $jobTitlesStmt->fetchAll();

        // Group job titles by grade
        foreach ($allJobTitles as $title) {
            $jobTitlesByGrade[$title['grade_id']][] = $title;
        }
    }
} catch (PDOException $e) {
    error_log("Get Reference Data Error: " . $e->getMessage());
    flash('error_message', 'حدث خطأ أثناء تحميل البيانات المرجعية', 'alert alert-danger');
    $departments = $educationLevels = [];
    $jobTitlesTableExists = false;
    $jobTitlesByGrade = [];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $fullName = sanitize($_POST['full_name']);
    $currentGrade = (int)$_POST['current_grade'];
    $currentStage = (int)$_POST['current_stage'];
    $jobTitle = sanitize($_POST['job_title']);
    $departmentId = (int)$_POST['department_id'];
    $educationLevelId = (int)$_POST['education_level_id'];
    $hireDate = sanitize($_POST['hire_date']);
    $birthDate = !empty($_POST['birth_date']) ? sanitize($_POST['birth_date']) : null;

    // HTML5 date inputs already send dates in yyyy-mm-dd format, no conversion needed
    // Just validate the date format
    if (!empty($hireDate) && !DateTime::createFromFormat('Y-m-d', $hireDate)) {
        $errors[] = 'تنسيق تاريخ التعيين غير صحيح';
    }
    if (!empty($birthDate) && !DateTime::createFromFormat('Y-m-d', $birthDate)) {
        $errors[] = 'تنسيق تاريخ الميلاد غير صحيح';
    }
    $allowancesInCurrentGrade = (int)$_POST['allowances_in_current_grade'];
    $lastAllowanceDate = !empty($_POST['last_allowance_date']) ? sanitize($_POST['last_allowance_date']) : null;
    $lastPromotionDate = !empty($_POST['last_promotion_date']) ? sanitize($_POST['last_promotion_date']) : null;
    $notes = sanitize($_POST['notes']);

    // Generate unique employee number automatically
    $employeeNumber = generateEmployeeNumber();

    // Calculate years of service based on hire date (only if hire date is provided)
    $yearsOfService = !empty($hireDate) ? calculateYearsOfService($hireDate) : 0;

    // Calculate years in current grade (assume they started in current grade)
    $yearsInCurrentGrade = $yearsOfService;

    // Validate inputs
    $errors = [];

    if (empty($fullName)) {
        $errors[] = 'اسم الموظف مطلوب';
    }

    if ($currentGrade < 1 || $currentGrade > 10) {
        $errors[] = 'الدرجة الوظيفية يجب أن تكون بين 1 و 10';
    }

    if (empty($jobTitle)) {
        $errors[] = 'العنوان الوظيفي مطلوب';
    }

    if ($departmentId <= 0) {
        $errors[] = 'يرجى اختيار القسم';
    }

    if ($educationLevelId <= 0) {
        $errors[] = 'يرجى اختيار التحصيل الدراسي';
    }

    if (empty($hireDate)) {
        $errors[] = 'تاريخ التعيين مطلوب';
    }

    // If no errors, insert employee
    if (empty($errors)) {
        try {
            // Calculate next allowance date
            $nextAllowanceDate = null;
            if ($lastAllowanceDate) {
                $nextAllowanceDate = calculateNextAllowanceDate($lastAllowanceDate);
            } else {
                // If no last allowance date, use hire date + 1 year
                $nextAllowanceDate = calculateNextAllowanceDate($hireDate);
            }

            // Calculate next promotion date
            $nextPromotionDate = null;
            if ($lastPromotionDate) {
                $nextPromotionDate = calculateNextPromotionDate($lastPromotionDate, $currentGrade);
            } else {
                // If no last promotion date, use hire date + required years
                // Get years needed from grade_service_years table
                try {
                    $serviceYearsStmt = $pdo->prepare("
                        SELECT service_years
                        FROM grade_service_years
                        WHERE grade_id = :grade_id
                    ");
                    $serviceYearsStmt->execute([':grade_id' => $currentGrade]);
                    $serviceYearsResult = $serviceYearsStmt->fetch();

                    if ($serviceYearsResult) {
                        $yearsNeeded = (int)$serviceYearsResult['service_years'];
                    } else {
                        // Fallback to constants if not found in table
                        $yearsNeeded = ($currentGrade >= 5) ? PROMOTION_YEARS_LOWER_GRADES : PROMOTION_YEARS_UPPER_GRADES;
                    }
                } catch (PDOException $e) {
                    error_log("Get Service Years Error: " . $e->getMessage());
                    // Fallback to constants if error
                    $yearsNeeded = ($currentGrade >= 5) ? PROMOTION_YEARS_LOWER_GRADES : PROMOTION_YEARS_UPPER_GRADES;
                }

                $hireDateObj = new DateTime($hireDate);
                $hireDateObj->add(new DateInterval('P' . $yearsNeeded . 'Y'));
                $nextPromotionDate = $hireDateObj->format('Y-m-d');
            }

            // Calculate retirement date if birth date is provided
            $retirementDate = null;
            if ($birthDate) {
                $retirementDate = calculateRetirementDate($birthDate);
            }

            // Get nominal salary based on grade and stage
            $nominalSalary = getNominalSalary($currentGrade, $currentStage);

            // Get maximum grade by education
            $maxGradeByEducation = getEducationMaxGrade($educationLevelId);

            // Insert employee
            $stmt = $pdo->prepare("
                INSERT INTO employees (
                    employee_number, full_name, current_grade, current_stage, nominal_salary, job_title,
                    department_id, education_level_id, hire_date, birth_date, retirement_date,
                    years_of_service, years_in_current_grade, allowances_in_current_grade,
                    appreciation_letters_count, last_allowance_date, last_promotion_date,
                    next_allowance_date, next_promotion_date, max_grade_by_education, notes
                ) VALUES (
                    :employee_number, :full_name, :current_grade, :current_stage, :nominal_salary, :job_title,
                    :department_id, :education_level_id, :hire_date, :birth_date, :retirement_date,
                    :years_of_service, :years_in_current_grade, :allowances_in_current_grade,
                    :appreciation_letters_count, :last_allowance_date, :last_promotion_date,
                    :next_allowance_date, :next_promotion_date, :max_grade_by_education, :notes
                )
            ");

            $stmt->execute([
                ':employee_number' => $employeeNumber,
                ':full_name' => $fullName,
                ':current_grade' => $currentGrade,
                ':current_stage' => $currentStage,
                ':nominal_salary' => $nominalSalary,
                ':job_title' => $jobTitle,
                ':department_id' => $departmentId,
                ':education_level_id' => $educationLevelId,
                ':hire_date' => $hireDate,
                ':birth_date' => $birthDate,
                ':retirement_date' => $retirementDate,
                ':years_of_service' => $yearsOfService,
                ':years_in_current_grade' => $yearsInCurrentGrade,
                ':allowances_in_current_grade' => $allowancesInCurrentGrade,
                ':appreciation_letters_count' => 0,
                ':last_allowance_date' => $lastAllowanceDate,
                ':last_promotion_date' => $lastPromotionDate,
                ':next_allowance_date' => $nextAllowanceDate,
                ':next_promotion_date' => $nextPromotionDate,
                ':max_grade_by_education' => $maxGradeByEducation,
                ':notes' => $notes
            ]);

            $employeeId = $pdo->lastInsertId();

            // Add salary history record
            if ($nominalSalary) {
                addSalaryHistory(
                    $employeeId,
                    $currentGrade,
                    $currentStage,
                    $nominalSalary,
                    $hireDate,
                    'initial',
                    'الراتب الاسمي الأولي'
                );
            }

            // Create retirement alert if retirement date is set
            if ($retirementDate) {
                createRetirementAlert($employeeId, $retirementDate);
            }

            // Log activity
            logActivity(
                $_SESSION['user_id'],
                'إضافة موظف',
                'employees',
                $employeeId,
                'تمت إضافة موظف جديد: ' . $fullName
            );

            // Redirect to employee details
            flash('success_message', 'تمت إضافة الموظف بنجاح', 'alert alert-success');
            redirect('employee_details.php?id=' . $employeeId);
        } catch (PDOException $e) {
            error_log("Add Employee Error: " . $e->getMessage());
            flash('error_message', 'حدث خطأ أثناء إضافة الموظف', 'alert alert-danger');
        }
    } else {
        // Display errors
        $errorMessage = '<ul class="mb-0">';
        foreach ($errors as $error) {
            $errorMessage .= '<li>' . $error . '</li>';
        }
        $errorMessage .= '</ul>';

        flash('error_message', $errorMessage, 'alert alert-danger');
    }
}
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="display-5 mb-0">
            <i class="fas fa-user-plus me-2"></i> إضافة موظف جديد
        </h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="employees.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى قائمة الموظفين
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post" class="needs-validation" novalidate>
            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="full_name" class="form-label">اسم الموظف <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="full_name" name="full_name" required>
                    <div class="invalid-feedback">يرجى إدخال اسم الموظف</div>
                    <div class="form-text">سيتم توليد الرقم الوظيفي تلقائياً</div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3">
                    <label for="current_grade" class="form-label">الدرجة الحالية <span class="text-danger">*</span></label>
                    <select class="form-select" id="current_grade" name="current_grade" required>
                        <option value="">اختر الدرجة</option>
                        <?php for ($i = 1; $i <= 10; $i++): ?>
                            <option value="<?php echo $i; ?>">الدرجة <?php echo $i; ?></option>
                        <?php endfor; ?>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار الدرجة الحالية</div>
                </div>
                <div class="col-md-3">
                    <label for="current_stage" class="form-label">المرحلة الحالية <span class="text-danger">*</span></label>
                    <select class="form-select" id="current_stage" name="current_stage" required>
                        <option value="">اختر المرحلة</option>
                        <?php for ($i = 1; $i <= 11; $i++): ?>
                            <option value="<?php echo $i; ?>">المرحلة <?php echo $i; ?></option>
                        <?php endfor; ?>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار المرحلة الحالية</div>
                </div>
                <div class="col-md-3">
                    <label for="job_title" class="form-label">العنوان الوظيفي <span class="text-danger">*</span></label>
                    <?php if ($jobTitlesTableExists): ?>
                        <select class="form-select" id="job_title" name="job_title" required>
                            <option value="">اختر العنوان الوظيفي</option>
                            <!-- Job titles will be loaded dynamically based on selected grade -->
                        </select>
                        <div class="invalid-feedback">يرجى اختيار العنوان الوظيفي</div>
                    <?php else: ?>
                        <input type="text" class="form-control" id="job_title" name="job_title" required>
                        <div class="invalid-feedback">يرجى إدخال العنوان الوظيفي</div>
                    <?php endif; ?>
                </div>
                <div class="col-md-3">
                    <label for="department_id" class="form-label">القسم <span class="text-danger">*</span></label>
                    <select class="form-select" id="department_id" name="department_id" required>
                        <option value="">اختر القسم</option>
                        <?php foreach ($departments as $department): ?>
                            <option value="<?php echo $department['id']; ?>"><?php echo $department['name']; ?></option>
                        <?php endforeach; ?>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار القسم</div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="education_level_id" class="form-label">التحصيل الدراسي <span class="text-danger">*</span></label>
                    <select class="form-select" id="education_level_id" name="education_level_id" required>
                        <option value="">اختر التحصيل الدراسي</option>
                        <?php foreach ($educationLevels as $level): ?>
                            <option value="<?php echo $level['id']; ?>"><?php echo $level['name']; ?> (الحد الأقصى: الدرجة <?php echo $level['max_grade']; ?>)</option>
                        <?php endforeach; ?>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار التحصيل الدراسي</div>
                </div>
                <div class="col-md-4">
                    <label for="hire_date" class="form-label">تاريخ التعيين <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="hire_date" name="hire_date" required>
                    <div class="invalid-feedback">يرجى إدخال تاريخ التعيين</div>
                    <div class="form-text">التنسيق: سنة-شهر-يوم (مثال: 2024-01-15)</div>
                </div>
                <div class="col-md-4">
                    <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                    <input type="date" class="form-control" id="birth_date" name="birth_date">
                    <div class="form-text">سيتم حساب تاريخ التقاعد تلقائياً (<?php echo RETIREMENT_AGE; ?> سنة)</div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="allowances_in_current_grade" class="form-label">عدد العلاوات في الدرجة الحالية</label>
                    <input type="number" class="form-control" id="allowances_in_current_grade" name="allowances_in_current_grade" min="0" max="11" value="0">
                    <div class="form-text">سيتم حساب سنوات الخدمة تلقائياً بناءً على تاريخ التعيين</div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="last_allowance_date" class="form-label">تاريخ آخر علاوة</label>
                    <input type="date" class="form-control" id="last_allowance_date" name="last_allowance_date">
                    <div class="form-text">اختياري - لحساب العلاوة القادمة</div>
                </div>
                <div class="col-md-4">
                    <label for="last_promotion_date" class="form-label">تاريخ آخر ترفيع</label>
                    <input type="date" class="form-control" id="last_promotion_date" name="last_promotion_date">
                    <div class="form-text">اختياري - لحساب الترفيع القادم</div>
                </div>
                <div class="col-md-4">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary px-5">
                    <i class="fas fa-save me-1"></i> حفظ
                </button>
                <a href="employees.php" class="btn btn-secondary px-5">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<?php if ($jobTitlesTableExists): ?>
<script>
// Store job titles by grade
const jobTitlesByGrade = <?php echo json_encode($jobTitlesByGrade); ?>;

// Function to update job titles dropdown based on selected grade
function updateJobTitles() {
    const gradeSelect = document.getElementById('current_grade');
    const jobTitleSelect = document.getElementById('job_title');

    // Clear current options
    jobTitleSelect.innerHTML = '<option value="">اختر العنوان الوظيفي</option>';

    // Get selected grade
    const selectedGrade = gradeSelect.value;

    if (selectedGrade && jobTitlesByGrade[selectedGrade]) {
        // Add job titles for selected grade
        jobTitlesByGrade[selectedGrade].forEach(title => {
            const option = document.createElement('option');
            option.value = title.title;
            option.textContent = title.title;
            if (title.description) {
                option.title = title.description;
            }
            jobTitleSelect.appendChild(option);
        });
    }
}

// Add event listener to grade select
document.addEventListener('DOMContentLoaded', function() {
    const gradeSelect = document.getElementById('current_grade');
    gradeSelect.addEventListener('change', updateJobTitles);

    // Initialize job titles if grade is already selected
    if (gradeSelect.value) {
        updateJobTitles();
    }
});
</script>
<?php endif; ?>

<!-- Date Input Enhancement Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Fix date input direction and add Arabic labels
    const dateInputs = document.querySelectorAll('input[type="date"]');

    dateInputs.forEach(function(input) {
        // Ensure LTR direction for date inputs
        input.style.direction = 'ltr';
        input.style.textAlign = 'left';

        // Add event listener to handle date validation
        input.addEventListener('change', function() {
            const dateValue = this.value;
            if (dateValue) {
                // Validate date format
                const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
                if (!dateRegex.test(dateValue)) {
                    this.setCustomValidity('تنسيق التاريخ غير صحيح');
                } else {
                    this.setCustomValidity('');
                }
            }
        });

        // Add focus and blur events for better UX
        input.addEventListener('focus', function() {
            this.style.borderColor = '#0d6efd';
            this.style.boxShadow = '0 0 0 0.25rem rgba(13, 110, 253, 0.25)';
        });

        input.addEventListener('blur', function() {
            this.style.borderColor = '';
            this.style.boxShadow = '';
        });
    });

    // Set maximum date for birth date (18 years ago)
    const birthDateInput = document.getElementById('birth_date');
    if (birthDateInput) {
        const today = new Date();
        const eighteenYearsAgo = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
        birthDateInput.max = eighteenYearsAgo.toISOString().split('T')[0];
    }

    // Set maximum date for hire date (today)
    const hireDateInput = document.getElementById('hire_date');
    if (hireDateInput) {
        const today = new Date();
        hireDateInput.max = today.toISOString().split('T')[0];
    }

    // Set maximum date for last allowance and promotion dates (today)
    const lastAllowanceInput = document.getElementById('last_allowance_date');
    const lastPromotionInput = document.getElementById('last_promotion_date');

    if (lastAllowanceInput) {
        const today = new Date();
        lastAllowanceInput.max = today.toISOString().split('T')[0];
    }

    if (lastPromotionInput) {
        const today = new Date();
        lastPromotionInput.max = today.toISOString().split('T')[0];
    }
});
</script>
